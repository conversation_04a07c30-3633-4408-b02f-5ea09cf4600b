using Serilog.Core;
using Serilog.Events;
using FlowCustomV1.Api.Services;
using System.Text.Json;

namespace FlowCustomV1.Api.Logging
{
    /// <summary>
    /// Serilog NATS Sink - 将日志发送到NATS
    /// </summary>
    public class NatsSink : ILogEventSink, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly Queue<LogEvent> _logQueue;
        private readonly Timer _batchTimer;
        private readonly object _lockObject = new();
        private readonly int _batchSize = 10;
        private readonly TimeSpan _batchInterval = TimeSpan.FromSeconds(2);
        private bool _disposed = false;

        public NatsSink(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logQueue = new Queue<LogEvent>();
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };

            // 启动批量处理定时器
            _batchTimer = new Timer(ProcessBatch, null, _batchInterval, _batchInterval);
        }

        public void Emit(LogEvent logEvent)
        {
            if (_disposed || logEvent == null)
                return;

            // 只处理重要级别的日志
            if (!ShouldProcessLevel(logEvent.Level))
                return;

            lock (_lockObject)
            {
                _logQueue.Enqueue(logEvent);

                // 如果队列满了，立即处理
                if (_logQueue.Count >= _batchSize)
                {
                    ProcessBatch(null);
                }
            }
        }

        private void ProcessBatch(object? state)
        {
            if (_disposed)
                return;

            List<LogEvent> batch;
            lock (_lockObject)
            {
                if (_logQueue.Count == 0)
                    return;

                batch = new List<LogEvent>();
                while (_logQueue.Count > 0 && batch.Count < _batchSize)
                {
                    batch.Add(_logQueue.Dequeue());
                }
            }

            // 异步处理批量日志
            _ = Task.Run(async () => await ProcessBatchAsync(batch));
        }

        private async Task ProcessBatchAsync(List<LogEvent> batch)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var natsPublisher = scope.ServiceProvider.GetService<IBackendLogNatsPublisher>();

                if (natsPublisher == null)
                    return;

                foreach (var logEvent in batch)
                {
                    try
                    {
                        var level = GetLevelString(logEvent.Level);
                        var category = GetCategory(logEvent);
                        var message = logEvent.RenderMessage();
                        var exception = logEvent.Exception?.ToString();

                        // 提取附加数据
                        var data = ExtractLogData(logEvent);

                        await natsPublisher.PublishLogAsync(level, category, message, data, exception);
                    }
                    catch (Exception ex)
                    {
                        // 避免日志循环，静默处理错误
                        System.Diagnostics.Debug.WriteLine($"NATS日志发布失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"NATS日志批量处理失败: {ex.Message}");
            }
        }

        private static bool ShouldProcessLevel(LogEventLevel level)
        {
            return level >= LogEventLevel.Warning; // 只处理警告及以上级别
        }

        private static string GetLevelString(LogEventLevel level)
        {
            return level switch
            {
                LogEventLevel.Verbose => "TRACE",
                LogEventLevel.Debug => "DEBUG",
                LogEventLevel.Information => "INFO",
                LogEventLevel.Warning => "WARNING",
                LogEventLevel.Error => "ERROR",
                LogEventLevel.Fatal => "FATAL",
                _ => "INFO"
            };
        }

        private static string GetCategory(LogEvent logEvent)
        {
            if (logEvent.Properties.TryGetValue("SourceContext", out var sourceContext))
            {
                return sourceContext.ToString().Trim('"');
            }
            return "Backend";
        }

        private object? ExtractLogData(LogEvent logEvent)
        {
            var data = new Dictionary<string, object?>();

            foreach (var property in logEvent.Properties)
            {
                if (property.Key == "SourceContext")
                    continue;

                try
                {
                    data[property.Key] = property.Value.ToString().Trim('"');
                }
                catch
                {
                    // 忽略无法序列化的属性
                }
            }

            return data.Count > 0 ? data : null;
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            // 处理剩余的日志
            ProcessBatch(null);

            _batchTimer?.Dispose();
        }
    }
}
