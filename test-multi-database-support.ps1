#!/usr/bin/env pwsh
# FlowCustomV1 多数据库支持测试脚本

param(
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# 测试API连接
function Test-ApiConnection {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5279/api/plugin/node-types/by-category" -Method GET -TimeoutSec 10
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

# 测试数据库配置
function Test-DatabaseConfiguration {
    param([string]$DatabaseType)
    
    Write-Log "🔍 测试 $DatabaseType 数据库配置..."
    
    # 更新appsettings.json
    $appsettingsPath = "src/FlowCustomV1.Api/appsettings.json"
    $appsettings = Get-Content $appsettingsPath | ConvertFrom-Json
    
    # 备份原始配置
    $originalProvider = $appsettings.Database.Provider
    
    try {
        # 设置数据库提供者
        $appsettings.Database.Provider = $DatabaseType
        $appsettings | ConvertTo-Json -Depth 10 | Set-Content $appsettingsPath
        
        Write-Log "✅ 已更新数据库配置为: $DatabaseType"
        
        # 重新编译项目
        Write-Log "🔨 重新编译项目..."
        $buildResult = dotnet build src/FlowCustomV1.Api/FlowCustomV1.Api.csproj --verbosity quiet
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "✅ 项目编译成功" -Level "SUCCESS"
            return $true
        } else {
            Write-Log "❌ 项目编译失败" -Level "ERROR"
            return $false
        }
        
    } finally {
        # 恢复原始配置
        $appsettings.Database.Provider = $originalProvider
        $appsettings | ConvertTo-Json -Depth 10 | Set-Content $appsettingsPath
    }
}

# 主测试函数
function Main {
    try {
        Write-Log "🚀 开始FlowCustomV1多数据库支持测试..."
        
        # 检查当前API状态
        Write-Log "🔍 检查当前API状态..."
        if (Test-ApiConnection) {
            Write-Log "✅ API当前运行正常" -Level "SUCCESS"
        } else {
            Write-Log "⚠️ API当前无法访问，但继续测试编译" -Level "WARN"
        }
        
        # 测试支持的数据库类型
        $databaseTypes = @("SQLite", "MySQL", "PostgreSQL")
        $testResults = @{}
        
        foreach ($dbType in $databaseTypes) {
            $testResults[$dbType] = Test-DatabaseConfiguration -DatabaseType $dbType
        }
        
        # 显示测试结果
        Write-Log "📊 测试结果汇总:" -Level "SUCCESS"
        foreach ($dbType in $databaseTypes) {
            $status = if ($testResults[$dbType]) { "✅ 支持" } else { "❌ 不支持" }
            $level = if ($testResults[$dbType]) { "SUCCESS" } else { "ERROR" }
            Write-Log "   $dbType : $status" -Level $level
        }
        
        # 检查数据库提供者实现
        Write-Log "🔍 检查数据库提供者实现..."
        $providerFiles = @(
            "src/FlowCustomV1.Data/Providers/SqliteProvider.cs",
            "src/FlowCustomV1.Data/Providers/MySqlProvider.cs", 
            "src/FlowCustomV1.Data/Providers/PostgreSqlProvider.cs"
        )
        
        foreach ($file in $providerFiles) {
            if (Test-Path $file) {
                Write-Log "✅ 找到提供者: $file" -Level "SUCCESS"
            } else {
                Write-Log "❌ 缺少提供者: $file" -Level "ERROR"
            }
        }
        
        # 检查NuGet包
        Write-Log "🔍 检查数据库NuGet包..."
        $projectFile = "src/FlowCustomV1.Data/FlowCustomV1.Data.csproj"
        $projectContent = Get-Content $projectFile -Raw
        
        $packages = @(
            "Microsoft.EntityFrameworkCore.Sqlite",
            "Pomelo.EntityFrameworkCore.MySql",
            "Npgsql.EntityFrameworkCore.PostgreSQL"
        )
        
        foreach ($package in $packages) {
            if ($projectContent -match $package) {
                Write-Log "✅ 找到NuGet包: $package" -Level "SUCCESS"
            } else {
                Write-Log "❌ 缺少NuGet包: $package" -Level "ERROR"
            }
        }
        
        # 检查数据库初始化服务
        Write-Log "🔍 检查数据库初始化服务..."
        $initServiceFile = "src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs"
        if (Test-Path $initServiceFile) {
            Write-Log "✅ 找到数据库初始化服务" -Level "SUCCESS"
        } else {
            Write-Log "❌ 缺少数据库初始化服务" -Level "ERROR"
        }
        
        # 总结
        $supportedCount = ($testResults.Values | Where-Object { $_ }).Count
        Write-Log "🎉 测试完成！支持 $supportedCount/3 种数据库类型" -Level "SUCCESS"
        
        if ($supportedCount -eq 3) {
            Write-Log "🌟 恭喜！FlowCustomV1完全支持多数据库架构" -Level "SUCCESS"
        } else {
            Write-Log "⚠️ 部分数据库类型需要进一步配置" -Level "WARN"
        }
        
    } catch {
        Write-Log "❌ 测试失败: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

# 执行主函数
Main
