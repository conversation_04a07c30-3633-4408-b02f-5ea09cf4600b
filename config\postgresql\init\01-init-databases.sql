-- FlowCustomV1 PostgreSQL 初始化脚本

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 创建Grafana数据库（用于监控）
CREATE DATABASE grafana 
WITH ENCODING 'UTF8' 
LC_COLLATE = 'C' 
LC_CTYPE = 'C' 
TEMPLATE template0;

-- 授权Grafana数据库权限
GRANT ALL PRIVILEGES ON DATABASE grafana TO flowcustom;

-- 连接到主数据库
\c flowcustom;

-- 创建系统配置表（如果不存在）
CREATE TABLE IF NOT EXISTS system_configurations (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(255) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type VARCHAR(50) NOT NULL DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为系统配置表创建更新时间触发器
DROP TRIGGER IF EXISTS update_system_configurations_updated_at ON system_configurations;
CREATE TRIGGER update_system_configurations_updated_at
    BEFORE UPDATE ON system_configurations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 插入默认系统配置
INSERT INTO system_configurations (config_key, config_value, config_type, description) VALUES
('system.version', 'v0.9.4', 'string', 'System version'),
('system.database.provider', 'PostgreSQL', 'string', 'Database provider type'),
('system.max_concurrent_workflows', '10', 'integer', 'Maximum concurrent workflow executions'),
('system.default_timeout_seconds', '300', 'integer', 'Default workflow timeout in seconds'),
('system.enable_performance_monitoring', 'true', 'boolean', 'Enable performance monitoring'),
('system.log_retention_days', '30', 'integer', 'Log retention period in days')
ON CONFLICT (config_key) DO NOTHING;

-- 创建性能监控视图
CREATE OR REPLACE VIEW performance_stats AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public';

-- 创建数据库大小监控视图
CREATE OR REPLACE VIEW database_size_stats AS
SELECT 
    pg_database.datname AS database_name,
    pg_size_pretty(pg_database_size(pg_database.datname)) AS size
FROM pg_database
WHERE pg_database.datname = 'flowcustom';

-- 创建表大小监控视图
CREATE OR REPLACE VIEW table_size_stats AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) AS table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) AS index_size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO flowcustom;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO flowcustom;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO flowcustom;

-- 显示初始化完成信息
SELECT 'FlowCustomV1 PostgreSQL database initialization completed successfully!' as message;
