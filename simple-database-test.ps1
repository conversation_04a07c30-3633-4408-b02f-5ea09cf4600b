#!/usr/bin/env pwsh
# 简单的多数据库支持测试

Write-Host "🚀 FlowCustomV1 多数据库支持测试" -ForegroundColor Green

# 1. 检查数据库提供者文件
Write-Host "`n📁 检查数据库提供者文件:" -ForegroundColor Yellow

$providers = @(
    "src/FlowCustomV1.Data/Providers/SqliteProvider.cs",
    "src/FlowCustomV1.Data/Providers/MySqlProvider.cs", 
    "src/FlowCustomV1.Data/Providers/PostgreSqlProvider.cs",
    "src/FlowCustomV1.Data/Providers/IDatabaseProvider.cs"
)

foreach ($file in $providers) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file" -ForegroundColor Red
    }
}

# 2. 检查NuGet包
Write-Host "`n📦 检查NuGet包:" -ForegroundColor Yellow

$projectFile = "src/FlowCustomV1.Data/FlowCustomV1.Data.csproj"
if (Test-Path $projectFile) {
    $content = Get-Content $projectFile -Raw
    
    $packages = @(
        "Microsoft.EntityFrameworkCore.Sqlite",
        "Pomelo.EntityFrameworkCore.MySql",
        "Npgsql.EntityFrameworkCore.PostgreSQL"
    )
    
    foreach ($package in $packages) {
        if ($content -match $package) {
            Write-Host "  ✅ $package" -ForegroundColor Green
        } else {
            Write-Host "  ❌ $package" -ForegroundColor Red
        }
    }
} else {
    Write-Host "  ❌ 项目文件不存在" -ForegroundColor Red
}

# 3. 检查配置文件
Write-Host "`n⚙️ 检查配置文件:" -ForegroundColor Yellow

$configFiles = @(
    "src/FlowCustomV1.Api/appsettings.json",
    "docker-compose.yml",
    "docker-compose.mysql.yml",
    "docker-compose.postgresql.yml"
)

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file" -ForegroundColor Red
    }
}

# 4. 检查数据库初始化服务
Write-Host "`n🔧 检查数据库初始化服务:" -ForegroundColor Yellow

$initService = "src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs"
if (Test-Path $initService) {
    Write-Host "  ✅ DatabaseInitializationService.cs" -ForegroundColor Green
} else {
    Write-Host "  ❌ DatabaseInitializationService.cs" -ForegroundColor Red
}

# 5. 编译测试
Write-Host "`n🔨 编译测试:" -ForegroundColor Yellow

try {
    $buildResult = dotnet build src/FlowCustomV1.Data/FlowCustomV1.Data.csproj --verbosity quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ FlowCustomV1.Data 编译成功" -ForegroundColor Green
    } else {
        Write-Host "  ❌ FlowCustomV1.Data 编译失败" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 编译过程出错: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. API连接测试
Write-Host "`n🌐 API连接测试:" -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5279/api/plugin/node-types/by-category" -Method GET -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "  ✅ API连接正常" -ForegroundColor Green
    } else {
        Write-Host "  ❌ API返回状态码: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ⚠️ API无法访问 (可能服务未启动)" -ForegroundColor Yellow
}

Write-Host "`n🎉 测试完成！" -ForegroundColor Green
