services:
  # PostgreSQL 数据库
  postgresql:
    image: postgres:16-alpine
    container_name: flowcustom-postgresql
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: flowcustom
      POSTGRES_USER: flowcustom
      POSTGRES_PASSWORD: FlowCustom@2025
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./config/postgresql/init:/docker-entrypoint-initdb.d
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    networks:
      - flowcustom-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U flowcustom -d flowcustom"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # NATS 消息服务器
  nats:
    image: nats:2.11.6-alpine
    container_name: flowcustom-nats
    ports:
      - "4222:4222"
      - "8222:8222"  # HTTP监控端口
      - "8080:8080"  # WebSocket端口
    command: [
      "--config", "/etc/nats/nats.conf"
    ]
    volumes:
      - nats_data:/data
      - type: bind
        source: ./nats.conf
        target: /etc/nats/nats.conf
        read_only: true
    networks:
      - flowcustom-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # 后端 API 服务
  api:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: flowcustom-api
    ports:
      - "5279:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - Database__Provider=PostgreSQL
      - ConnectionStrings__PostgreSQL=Host=postgresql;Port=5432;Database=flowcustom;Username=flowcustom;Password=FlowCustom@2025;
      - ConnectionStrings__NATS=nats://nats:4222
      - Logging__LogLevel__Default=Information
      - Logging__LogLevel__Microsoft.AspNetCore=Warning
      - FrontendLogging__EnableFileOutput=true
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      postgresql:
        condition: service_healthy
      nats:
        condition: service_healthy
    networks:
      - flowcustom-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/plugin/node-types/by-category"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: flowcustom-frontend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=http://localhost:5279/api
      - VITE_NATS_WS_URL=ws://localhost:8080
    depends_on:
      - api
    networks:
      - flowcustom-network
    restart: unless-stopped

volumes:
  postgresql_data:
    driver: local
  nats_data:
    driver: local

networks:
  flowcustom-network:
    driver: bridge
