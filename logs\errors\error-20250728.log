[2025-07-28 12:59:03.046 +00:00 ERR] Program: 后台初始化失败
MySqlConnector.MySqlException (0x80004005): Unable to connect to any of the specified MySQL hosts.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1063
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at FlowCustomV1.Data.Providers.MySqlProvider.ConfigureDbContext(DbContextOptionsBuilder builder, String connectionString) in /src/src/FlowCustomV1.Data/Providers/MySqlProvider.cs:line 17
   at Program.<>c__DisplayClass0_0.<<Main>$>b__20(IServiceProvider serviceProvider, DbContextOptionsBuilder options) in /src/src/FlowCustomV1.Api/Program.cs:line 85
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c__DisplayClass0_0.<<<Main>$>b__10>d.MoveNext() in /src/src/FlowCustomV1.Api/Program.cs:line 288
[2025-07-28 13:10:23.957 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Configuration`, `w`.`Connections`, `w`.`CreatedAt`, `w`.`CreatedBy`, `w`.`Description`, `w`.`IsEnabled`, `w`.`Name`, `w`.`Nodes`, `w`.`UpdatedAt`, `w`.`Variables`, `w`.`Version`, `w`.`VersionDescription`, `w`.`VersionTag`
FROM `workflowdefinitions` AS `w`
ORDER BY `w`.`Name`
[2025-07-28 13:10:23.972 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:10:23.977 +00:00 ERR] FlowCustomV1.Data.Repositories.WorkflowRepository: 获取所有工作流定义失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
[2025-07-28 13:10:23.982 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 获取工作流列表失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflows(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 48
[2025-07-28 13:10:24.032 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Configuration`, `w`.`Connections`, `w`.`CreatedAt`, `w`.`CreatedBy`, `w`.`Description`, `w`.`IsEnabled`, `w`.`Name`, `w`.`Nodes`, `w`.`UpdatedAt`, `w`.`Variables`, `w`.`Version`, `w`.`VersionDescription`, `w`.`VersionTag`
FROM `workflowdefinitions` AS `w`
ORDER BY `w`.`Name`
[2025-07-28 13:10:24.035 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:10:24.039 +00:00 ERR] FlowCustomV1.Data.Repositories.WorkflowRepository: 获取所有工作流定义失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
[2025-07-28 13:10:24.043 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 获取工作流列表失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflows(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 48
[2025-07-28 13:17:41.255 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (23ms) [Parameters=[@__name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `workflowdefinitions` AS `w`
    WHERE `w`.`Name` = @__name_0)
[2025-07-28 13:17:41.260 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:17:41.265 +00:00 ERR] FlowCustomV1.Data.Repositories.WorkflowRepository: 检查工作流名称是否存在失败: 新工作流_1753708660866
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.ExistsAsync(String name, Nullable`1 excludeId, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 207
[2025-07-28 13:17:41.268 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: 新工作流_1753708660866
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.ExistsAsync(String name, Nullable`1 excludeId, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 207
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 114
[2025-07-28 13:30:45.028 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 13:30:45.058 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 13:30:45.444 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `workflowdefinitions` AS `w`)
[2025-07-28 13:30:45.455 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:33:24.030 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Configuration`, `w`.`Connections`, `w`.`CreatedAt`, `w`.`CreatedBy`, `w`.`Description`, `w`.`IsEnabled`, `w`.`Name`, `w`.`Nodes`, `w`.`UpdatedAt`, `w`.`Variables`, `w`.`Version`, `w`.`VersionDescription`, `w`.`VersionTag`
FROM `workflowdefinitions` AS `w`
ORDER BY `w`.`Name`
[2025-07-28 13:33:24.035 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:33:24.042 +00:00 ERR] FlowCustomV1.Data.Repositories.WorkflowRepository: 获取所有工作流定义失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
[2025-07-28 13:33:24.049 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 获取工作流列表失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflows(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 48
[2025-07-28 13:33:24.147 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Configuration`, `w`.`Connections`, `w`.`CreatedAt`, `w`.`CreatedBy`, `w`.`Description`, `w`.`IsEnabled`, `w`.`Name`, `w`.`Nodes`, `w`.`UpdatedAt`, `w`.`Variables`, `w`.`Version`, `w`.`VersionDescription`, `w`.`VersionTag`
FROM `workflowdefinitions` AS `w`
ORDER BY `w`.`Name`
[2025-07-28 13:33:24.150 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:33:24.156 +00:00 ERR] FlowCustomV1.Data.Repositories.WorkflowRepository: 获取所有工作流定义失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
[2025-07-28 13:33:24.161 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 获取工作流列表失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflows(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 48
[2025-07-28 13:33:26.823 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:jvae385a] [ERROR] [Console] ❌ 订阅后端日志失败: Error: natsService.isConnected is not a function
TypeError: natsService.isConnected is not a function
    at checkConnection (http://localhost:5173/src/services/logService.ts:278:27)
    at http://localhost:5173/src/services/logService.ts:284:9
    at new Promise (<anonymous>)
    at LogService.subscribeToBackendLogs (http://localhost:5173/src/services/logService.ts:276:13)
    at new LogService (http://localhost:5173/src/services/logService.ts:43:10)
    at LogService.getInstance (http://localhost:5173/src/services/logService.ts:47:29)
    at http://localhost:5173/src/components/LogManager.tsx:37:31 URL: http://localhost:5173/ {"Id":"1753709603796-royjbyqu8","SessionId":"session_1753709603792_zjvae385a","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:26.838 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:jvae385a] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1564:7) URL: http://localhost:5173/ {"Id":"1753709604064-0xmlbaunq","SessionId":"session_1753709603792_zjvae385a","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:26.839 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:jvae385a] [ERROR] [Console] ❌ 加载工作流列表失败: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:2122:41)
    at async Object.getAll (http://localhost:5173/src/services/api.ts:38:22)
    at async WorkflowOperationService.loadWorkflows (http://localhost:5173/src/services/workflowOperationService.ts:210:25)
    at async http://localhost:5173/src/hooks/useWorkflowOperations.ts:94:25 URL: http://localhost:5173/ {"Id":"1753709604065-sr775bt78","SessionId":"session_1753709603792_zjvae385a","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:26.842 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:jvae385a] [ERROR] [Global] Unhandled Promise Rejection: AxiosError: Request failed with status code 500 URL: http://localhost:5173/ {"Id":"1753709604073-fd4t28a1a","SessionId":"session_1753709603792_zjvae385a","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Global","Data":{"reason":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:2122:41)\n    at async Object.getAll (http://localhost:5173/src/services/api.ts:38:22)\n    at async WorkflowOperationService.loadWorkflows (http://localhost:5173/src/services/workflowOperationService.ts:210:25)\n    at async http://localhost:5173/src/hooks/useWorkflowOperations.ts:94:25","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"get","url":"/workflows","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 13:33:26.843 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:jvae385a] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1564:7) URL: http://localhost:5173/ {"Id":"1753709604168-luba804w8","SessionId":"session_1753709603792_zjvae385a","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:26.844 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:jvae385a] [ERROR] [Console] ❌ 加载工作流列表失败: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:2122:41)
    at async Object.getAll (http://localhost:5173/src/services/api.ts:38:22)
    at async WorkflowOperationService.loadWorkflows (http://localhost:5173/src/services/workflowOperationService.ts:210:25)
    at async http://localhost:5173/src/hooks/useWorkflowOperations.ts:94:25 URL: http://localhost:5173/ {"Id":"1753709604168-t75rr7p8m","SessionId":"session_1753709603792_zjvae385a","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:26.845 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:jvae385a] [ERROR] [Global] Unhandled Promise Rejection: AxiosError: Request failed with status code 500 URL: http://localhost:5173/ {"Id":"1753709604175-a7w5kydrn","SessionId":"session_1753709603792_zjvae385a","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Global","Data":{"reason":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=406c43fc:2122:41)\n    at async Object.getAll (http://localhost:5173/src/services/api.ts:38:22)\n    at async WorkflowOperationService.loadWorkflows (http://localhost:5173/src/services/workflowOperationService.ts:210:25)\n    at async http://localhost:5173/src/hooks/useWorkflowOperations.ts:94:25","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"get","url":"/workflows","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 13:33:55.842 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Configuration`, `w`.`Connections`, `w`.`CreatedAt`, `w`.`CreatedBy`, `w`.`Description`, `w`.`IsEnabled`, `w`.`Name`, `w`.`Nodes`, `w`.`UpdatedAt`, `w`.`Variables`, `w`.`Version`, `w`.`VersionDescription`, `w`.`VersionTag`
FROM `workflowdefinitions` AS `w`
ORDER BY `w`.`Name`
[2025-07-28 13:33:55.846 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:33:55.854 +00:00 ERR] FlowCustomV1.Data.Repositories.WorkflowRepository: 获取所有工作流定义失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
[2025-07-28 13:33:55.862 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 获取工作流列表失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflows(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 48
[2025-07-28 13:33:55.889 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Configuration`, `w`.`Connections`, `w`.`CreatedAt`, `w`.`CreatedBy`, `w`.`Description`, `w`.`IsEnabled`, `w`.`Name`, `w`.`Nodes`, `w`.`UpdatedAt`, `w`.`Variables`, `w`.`Version`, `w`.`VersionDescription`, `w`.`VersionTag`
FROM `workflowdefinitions` AS `w`
ORDER BY `w`.`Name`
[2025-07-28 13:33:55.892 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:33:55.898 +00:00 ERR] FlowCustomV1.Data.Repositories.WorkflowRepository: 获取所有工作流定义失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
[2025-07-28 13:33:55.904 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 获取工作流列表失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflows(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 48
[2025-07-28 13:33:58.765 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Console] ❌ 订阅后端日志失败: Error: natsService.isConnected is not a function
TypeError: natsService.isConnected is not a function
    at checkConnection (http://localhost:5173/src/services/logService.ts:278:27)
    at http://localhost:5173/src/services/logService.ts:284:9
    at new Promise (<anonymous>)
    at LogService.subscribeToBackendLogs (http://localhost:5173/src/services/logService.ts:276:13)
    at new LogService (http://localhost:5173/src/services/logService.ts:43:10)
    at LogService.getInstance (http://localhost:5173/src/services/logService.ts:47:29)
    at http://localhost:5173/src/components/LogManager.tsx:37:31 URL: http://localhost:5173/ {"Id":"1753709635751-0ad7z3qdw","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:58.766 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7) URL: http://localhost:5173/ {"Id":"1753709635886-de035r2vb","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:58.767 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Console] ❌ 加载工作流列表失败: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.getAll (http://localhost:5173/src/services/api.ts:38:22)
    at async WorkflowOperationService.loadWorkflows (http://localhost:5173/src/services/workflowOperationService.ts:210:25)
    at async http://localhost:5173/src/hooks/useWorkflowOperations.ts:94:25 URL: http://localhost:5173/ {"Id":"1753709635887-m50i4yn8k","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:58.768 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Global] Unhandled Promise Rejection: AxiosError: Request failed with status code 500 URL: http://localhost:5173/ {"Id":"1753709635898-z2ea4kvjk","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Global","Data":{"reason":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)\n    at async Object.getAll (http://localhost:5173/src/services/api.ts:38:22)\n    at async WorkflowOperationService.loadWorkflows (http://localhost:5173/src/services/workflowOperationService.ts:210:25)\n    at async http://localhost:5173/src/hooks/useWorkflowOperations.ts:94:25","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"get","url":"/workflows","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 13:33:58.769 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7) URL: http://localhost:5173/ {"Id":"1753709635914-gx0afcwa4","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:58.770 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Console] ❌ 加载工作流列表失败: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.getAll (http://localhost:5173/src/services/api.ts:38:22)
    at async WorkflowOperationService.loadWorkflows (http://localhost:5173/src/services/workflowOperationService.ts:210:25)
    at async http://localhost:5173/src/hooks/useWorkflowOperations.ts:94:25 URL: http://localhost:5173/ {"Id":"1753709635914-bahwjvqg0","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:33:58.771 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Global] Unhandled Promise Rejection: AxiosError: Request failed with status code 500 URL: http://localhost:5173/ {"Id":"1753709635921-6xopzs6is","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Global","Data":{"reason":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)\n    at async Object.getAll (http://localhost:5173/src/services/api.ts:38:22)\n    at async WorkflowOperationService.loadWorkflows (http://localhost:5173/src/services/workflowOperationService.ts:210:25)\n    at async http://localhost:5173/src/hooks/useWorkflowOperations.ts:94:25","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"get","url":"/workflows","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 13:33:58.772 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753709637859-twuyixvr2","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 13:33:58.773 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753709637877-yjlornbud","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 13:34:45.323 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (36ms) [Parameters=[@__name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `workflowdefinitions` AS `w`
    WHERE `w`.`Name` = @__name_0)
[2025-07-28 13:34:45.326 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:34:45.331 +00:00 ERR] FlowCustomV1.Data.Repositories.WorkflowRepository: 检查工作流名称是否存在失败: 新工作流_1753709685063
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.ExistsAsync(String name, Nullable`1 excludeId, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 207
[2025-07-28 13:34:45.337 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: 新工作流_1753709685063
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.ExistsAsync(String name, Nullable`1 excludeId, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 207
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 114
[2025-07-28 13:34:46.764 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7) URL: http://localhost:5173/ {"Id":"1753709685345-xqc0nyyfq","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:34:46.766 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Console] Create workflow error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753709685346-7dcqpyqwi","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:34:46.767 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [App] 创建工作流失败 URL: http://localhost:5173/ {"Id":"1753709685376-4ahlrwgln","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":{"error":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)\n    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)\n    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)\n    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24)","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"post","url":"/workflows","data":"{\u0022id\u0022:\u002200000000-0000-0000-0000-000000000000\u0022,\u0022name\u0022:\u0022\u65B0\u5DE5\u4F5C\u6D41_1753709685063\u0022,\u0022description\u0022:\u0022\u65B0\u521B\u5EFA\u7684\u5DE5\u4F5C\u6D41\u0022,\u0022version\u0022:1,\u0022isEnabled\u0022:true,\u0022createdAt\u0022:\u00222025-07-28T13:34:45.064Z\u0022,\u0022updatedAt\u0022:\u00222025-07-28T13:34:45.064Z\u0022,\u0022createdBy\u0022:\u0022user\u0022,\u0022nodes\u0022:[],\u0022connections\u0022:[],\u0022variables\u0022:{},\u0022configuration\u0022:{\u0022timeoutSeconds\u0022:300,\u0022maxRetryCount\u0022:3,\u0022enableParallelExecution\u0022:true,\u0022errorHandling\u0022:\u0022StopOnError\u0022,\u0022logLevel\u0022:\u0022Information\u0022}}","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 13:34:46.768 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:dhylbpd7] [ERROR] [Console] Failed to create workflow: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753709685376-sawwyc6v2","SessionId":"session_1753709635749_sdhylbpd7","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:36:07.884 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Configuration`, `w`.`Connections`, `w`.`CreatedAt`, `w`.`CreatedBy`, `w`.`Description`, `w`.`IsEnabled`, `w`.`Name`, `w`.`Nodes`, `w`.`UpdatedAt`, `w`.`Variables`, `w`.`Version`, `w`.`VersionDescription`, `w`.`VersionTag`
FROM `workflowdefinitions` AS `w`
ORDER BY `w`.`Name`
[2025-07-28 13:36:07.886 +00:00 ERR] Microsoft.EntityFrameworkCore.Query: An exception occurred while iterating over the results of a query for context type 'FlowCustomV1.Data.FlowCustomDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
[2025-07-28 13:36:07.889 +00:00 ERR] FlowCustomV1.Data.Repositories.WorkflowRepository: 获取所有工作流定义失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
[2025-07-28 13:36:07.892 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 获取工作流列表失败
MySqlConnector.MySqlException (0x80004005): Table 'flowcustom.workflowdefinitions' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.<>c__DisplayClass30_0`2.<<ExecuteAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteImplementationAsync[TState,TResult](Func`4 operation, Func`4 verifySucceeded, TState state, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.ExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at FlowCustomV1.Data.Repositories.WorkflowRepository.GetAllAsync(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Data/Repositories/WorkflowRepository.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflows(CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 48
[2025-07-28 13:43:04.990 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 13:43:05.006 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 13:45:35.201 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:h498ezpz] [ERROR] [Console] ❌ 订阅后端日志失败: Error: natsService.isConnected is not a function
TypeError: natsService.isConnected is not a function
    at checkConnection (http://localhost:5173/src/services/logService.ts:278:27)
    at http://localhost:5173/src/services/logService.ts:284:9
    at new Promise (<anonymous>)
    at LogService.subscribeToBackendLogs (http://localhost:5173/src/services/logService.ts:276:13)
    at new LogService (http://localhost:5173/src/services/logService.ts:43:10)
    at LogService.getInstance (http://localhost:5173/src/services/logService.ts:47:29)
    at http://localhost:5173/src/components/LogManager.tsx:37:31 URL: http://localhost:5173/ {"Id":"1753710332161-7rz1nc575","SessionId":"session_1753710332159_th498ezpz","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:45:35.221 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:h498ezpz] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753710334413-5trv8x0js","SessionId":"session_1753710332159_th498ezpz","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 13:45:35.224 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:h498ezpz] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753710334453-qqcom6yq3","SessionId":"session_1753710332159_th498ezpz","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 13:45:48.028 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: 新工作流_1753710347898
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 13:45:50.174 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:h498ezpz] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7) URL: http://localhost:5173/ {"Id":"1753710348033-c2o3fkg17","SessionId":"session_1753710332159_th498ezpz","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:45:50.175 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:h498ezpz] [ERROR] [Console] Create workflow error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753710348033-h5eafpupl","SessionId":"session_1753710332159_th498ezpz","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:45:50.177 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:h498ezpz] [ERROR] [App] 创建工作流失败 URL: http://localhost:5173/ {"Id":"1753710348049-vqd5zlrsp","SessionId":"session_1753710332159_th498ezpz","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":{"error":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)\n    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)\n    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)\n    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24)","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"post","url":"/workflows","data":"{\u0022id\u0022:\u002200000000-0000-0000-0000-000000000000\u0022,\u0022name\u0022:\u0022\u65B0\u5DE5\u4F5C\u6D41_1753710347898\u0022,\u0022description\u0022:\u0022\u65B0\u521B\u5EFA\u7684\u5DE5\u4F5C\u6D41\u0022,\u0022version\u0022:1,\u0022isEnabled\u0022:true,\u0022createdAt\u0022:\u00222025-07-28T13:45:47.898Z\u0022,\u0022updatedAt\u0022:\u00222025-07-28T13:45:47.898Z\u0022,\u0022createdBy\u0022:\u0022user\u0022,\u0022nodes\u0022:[],\u0022connections\u0022:[],\u0022variables\u0022:{},\u0022configuration\u0022:{\u0022timeoutSeconds\u0022:300,\u0022maxRetryCount\u0022:3,\u0022enableParallelExecution\u0022:true,\u0022errorHandling\u0022:\u0022StopOnError\u0022,\u0022logLevel\u0022:\u0022Information\u0022}}","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 13:45:50.178 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:h498ezpz] [ERROR] [Console] Failed to create workflow: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753710348049-9tgetv40y","SessionId":"session_1753710332159_th498ezpz","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:46:25.621 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 13:50:52.430 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 13:50:52.454 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 13:51:05.937 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 13:55:54.192 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:ny74c2l8] [ERROR] [Console] ❌ 订阅后端日志失败: Error: natsService.isConnected is not a function
TypeError: natsService.isConnected is not a function
    at checkConnection (http://localhost:5173/src/services/logService.ts:278:27)
    at http://localhost:5173/src/services/logService.ts:284:9
    at new Promise (<anonymous>)
    at LogService.subscribeToBackendLogs (http://localhost:5173/src/services/logService.ts:276:13)
    at new LogService (http://localhost:5173/src/services/logService.ts:43:10)
    at LogService.getInstance (http://localhost:5173/src/services/logService.ts:47:29)
    at http://localhost:5173/src/components/LogManager.tsx:37:31 URL: http://localhost:5173/ {"Id":"1753710951036-ygblrtkcx","SessionId":"session_1753710951030_cny74c2l8","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:55:54.303 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:ny74c2l8] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753710953448-lrwjn525l","SessionId":"session_1753710951030_cny74c2l8","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 13:55:54.308 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:ny74c2l8] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753710953505-qt4lgnz4u","SessionId":"session_1753710951030_cny74c2l8","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 13:56:14.078 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 13:56:14.099 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 13:56:33.187 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 13:56:58.529 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: 新工作流_1753711018457
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 13:57:00.051 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:ny74c2l8] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7) URL: http://localhost:5173/ {"Id":"1753711018532-9wwec4n33","SessionId":"session_1753710951030_cny74c2l8","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:57:00.063 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:ny74c2l8] [ERROR] [Console] Create workflow error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753711018533-5g23q6jgx","SessionId":"session_1753710951030_cny74c2l8","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:57:00.065 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:ny74c2l8] [ERROR] [App] 创建工作流失败 URL: http://localhost:5173/ {"Id":"1753711018548-ejvcrgzjk","SessionId":"session_1753710951030_cny74c2l8","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":{"error":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)\n    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)\n    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)\n    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24)","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"post","url":"/workflows","data":"{\u0022id\u0022:\u002200000000-0000-0000-0000-000000000000\u0022,\u0022name\u0022:\u0022\u65B0\u5DE5\u4F5C\u6D41_1753711018457\u0022,\u0022description\u0022:\u0022\u65B0\u521B\u5EFA\u7684\u5DE5\u4F5C\u6D41\u0022,\u0022version\u0022:1,\u0022isEnabled\u0022:true,\u0022createdAt\u0022:\u00222025-07-28T13:56:58.458Z\u0022,\u0022updatedAt\u0022:\u00222025-07-28T13:56:58.458Z\u0022,\u0022createdBy\u0022:\u0022user\u0022,\u0022nodes\u0022:[],\u0022connections\u0022:[],\u0022variables\u0022:{},\u0022configuration\u0022:{\u0022timeoutSeconds\u0022:300,\u0022maxRetryCount\u0022:3,\u0022enableParallelExecution\u0022:true,\u0022errorHandling\u0022:\u0022StopOnError\u0022,\u0022logLevel\u0022:\u0022Information\u0022}}","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 13:57:00.065 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:ny74c2l8] [ERROR] [Console] Failed to create workflow: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753711018548-ywi8m79r9","SessionId":"session_1753710951030_cny74c2l8","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 13:58:48.661 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 13:58:48.677 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 13:59:02.860 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:01:14.057 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:1c17uhvq] [ERROR] [Console] ❌ 订阅后端日志失败: Error: natsService.isConnected is not a function
TypeError: natsService.isConnected is not a function
    at checkConnection (http://localhost:5173/src/services/logService.ts:278:27)
    at http://localhost:5173/src/services/logService.ts:284:9
    at new Promise (<anonymous>)
    at LogService.subscribeToBackendLogs (http://localhost:5173/src/services/logService.ts:276:13)
    at new LogService (http://localhost:5173/src/services/logService.ts:43:10)
    at LogService.getInstance (http://localhost:5173/src/services/logService.ts:47:29)
    at http://localhost:5173/src/components/LogManager.tsx:37:31 URL: http://localhost:5173/ {"Id":"1753711270835-0qmhaujj1","SessionId":"session_1753711270828_j1c17uhvq","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:01:14.196 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:1c17uhvq] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753711273203-imendjquz","SessionId":"session_1753711270828_j1c17uhvq","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 14:01:14.211 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:1c17uhvq] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753711273267-nsc6o1718","SessionId":"session_1753711270828_j1c17uhvq","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 14:02:07.185 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 14:02:07.201 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 14:02:26.244 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:03:09.177 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: 新工作流_1753711389091
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:03:10.858 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:1c17uhvq] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7) URL: http://localhost:5173/ {"Id":"1753711389184-ouhxbqe7h","SessionId":"session_1753711270828_j1c17uhvq","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:03:10.872 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:1c17uhvq] [ERROR] [Console] Create workflow error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753711389185-g8xbk2hwf","SessionId":"session_1753711270828_j1c17uhvq","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:03:10.875 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:1c17uhvq] [ERROR] [App] 创建工作流失败 URL: http://localhost:5173/ {"Id":"1753711389213-q24ikw1fp","SessionId":"session_1753711270828_j1c17uhvq","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":{"error":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)\n    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)\n    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)\n    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24)","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"post","url":"/workflows","data":"{\u0022id\u0022:\u002200000000-0000-0000-0000-000000000000\u0022,\u0022name\u0022:\u0022\u65B0\u5DE5\u4F5C\u6D41_1753711389091\u0022,\u0022description\u0022:\u0022\u65B0\u521B\u5EFA\u7684\u5DE5\u4F5C\u6D41\u0022,\u0022version\u0022:1,\u0022isEnabled\u0022:true,\u0022createdAt\u0022:\u00222025-07-28T14:03:09.092Z\u0022,\u0022updatedAt\u0022:\u00222025-07-28T14:03:09.092Z\u0022,\u0022createdBy\u0022:\u0022user\u0022,\u0022nodes\u0022:[],\u0022connections\u0022:[],\u0022variables\u0022:{},\u0022configuration\u0022:{\u0022timeoutSeconds\u0022:300,\u0022maxRetryCount\u0022:3,\u0022enableParallelExecution\u0022:true,\u0022errorHandling\u0022:\u0022StopOnError\u0022,\u0022logLevel\u0022:\u0022Information\u0022}}","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 14:03:10.876 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:1c17uhvq] [ERROR] [Console] Failed to create workflow: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753711389214-49nk60ns2","SessionId":"session_1753711270828_j1c17uhvq","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:05:01.579 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 14:05:01.600 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 14:05:23.936 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:08:17.839 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 14:08:17.858 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 14:08:35.386 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:10:35.306 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:vnkeyt8s] [ERROR] [Console] ❌ 订阅后端日志失败: Error: natsService.isConnected is not a function
TypeError: natsService.isConnected is not a function
    at checkConnection (http://localhost:5173/src/services/logService.ts:278:27)
    at http://localhost:5173/src/services/logService.ts:284:9
    at new Promise (<anonymous>)
    at LogService.subscribeToBackendLogs (http://localhost:5173/src/services/logService.ts:276:13)
    at new LogService (http://localhost:5173/src/services/logService.ts:43:10)
    at LogService.getInstance (http://localhost:5173/src/services/logService.ts:47:29)
    at http://localhost:5173/src/components/LogManager.tsx:37:31 URL: http://localhost:5173/ {"Id":"1753711832219-6ip6l4sub","SessionId":"session_1753711832212_5vnkeyt8s","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:10:35.364 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:vnkeyt8s] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753711834624-5btiu6moe","SessionId":"session_1753711832212_5vnkeyt8s","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 14:10:35.369 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:vnkeyt8s] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753711834696-wb2mgsb6y","SessionId":"session_1753711832212_5vnkeyt8s","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 14:11:15.517 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: 新工作流_1753711875407
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:11:17.222 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:vnkeyt8s] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7) URL: http://localhost:5173/ {"Id":"1753711875524-f2gwfocqb","SessionId":"session_1753711832212_5vnkeyt8s","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:11:17.223 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:vnkeyt8s] [ERROR] [Console] Create workflow error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753711875525-69s8q8q4y","SessionId":"session_1753711832212_5vnkeyt8s","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:11:17.226 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:vnkeyt8s] [ERROR] [App] 创建工作流失败 URL: http://localhost:5173/ {"Id":"1753711875546-wmqivsvtr","SessionId":"session_1753711832212_5vnkeyt8s","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":{"error":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)\n    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)\n    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)\n    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24)","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"post","url":"/workflows","data":"{\u0022id\u0022:\u002200000000-0000-0000-0000-000000000000\u0022,\u0022name\u0022:\u0022\u65B0\u5DE5\u4F5C\u6D41_1753711875407\u0022,\u0022description\u0022:\u0022\u65B0\u521B\u5EFA\u7684\u5DE5\u4F5C\u6D41\u0022,\u0022version\u0022:1,\u0022isEnabled\u0022:true,\u0022createdAt\u0022:\u00222025-07-28T14:11:15.407Z\u0022,\u0022updatedAt\u0022:\u00222025-07-28T14:11:15.407Z\u0022,\u0022createdBy\u0022:\u0022user\u0022,\u0022nodes\u0022:[],\u0022connections\u0022:[],\u0022variables\u0022:{},\u0022configuration\u0022:{\u0022timeoutSeconds\u0022:300,\u0022maxRetryCount\u0022:3,\u0022enableParallelExecution\u0022:true,\u0022errorHandling\u0022:\u0022StopOnError\u0022,\u0022logLevel\u0022:\u0022Information\u0022}}","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 14:11:17.227 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:vnkeyt8s] [ERROR] [Console] Failed to create workflow: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753711875546-nc06xousz","SessionId":"session_1753711832212_5vnkeyt8s","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:11:26.115 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 14:11:26.135 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 14:11:50.457 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:14:40.899 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:16:48.340 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:19:30.577 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 14:19:30.599 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 14:20:12.620 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:22:58.301 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: 新工作流_1753712578142
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
[2025-07-28 14:22:58.520 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:kvw21fhv] [ERROR] [Console] ❌ 订阅后端日志失败: Error: natsService.isConnected is not a function
TypeError: natsService.isConnected is not a function
    at checkConnection (http://localhost:5173/src/services/logService.ts:278:27)
    at http://localhost:5173/src/services/logService.ts:284:9
    at new Promise (<anonymous>)
    at LogService.subscribeToBackendLogs (http://localhost:5173/src/services/logService.ts:276:13)
    at new LogService (http://localhost:5173/src/services/logService.ts:43:10)
    at LogService.getInstance (http://localhost:5173/src/services/logService.ts:47:29)
    at http://localhost:5173/src/components/LogManager.tsx:37:31 URL: http://localhost:5173/ {"Id":"1753712575474-8h1x607xm","SessionId":"session_1753712575472_ekvw21fhv","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:22:58.556 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:kvw21fhv] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753712577826-dure588sm","SessionId":"session_1753712575472_ekvw21fhv","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 14:22:58.559 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:kvw21fhv] [ERROR] [App] 这是一条错误日志 URL: http://localhost:5173/ {"Id":"1753712577893-oxrctx712","SessionId":"session_1753712575472_ekvw21fhv","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":null,"StackTrace":null}
[2025-07-28 14:22:58.561 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:kvw21fhv] [ERROR] [Console] 🚨 API Response Error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7) URL: http://localhost:5173/ {"Id":"1753712578308-g3by7wtyu","SessionId":"session_1753712575472_ekvw21fhv","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:22:58.564 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:kvw21fhv] [ERROR] [Console] Create workflow error: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753712578309-2h7522psu","SessionId":"session_1753712575472_ekvw21fhv","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:22:58.573 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:kvw21fhv] [ERROR] [App] 创建工作流失败 URL: http://localhost:5173/ {"Id":"1753712578355-mi1b07wf4","SessionId":"session_1753712575472_ekvw21fhv","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"App","Data":{"error":{"message":"Request failed with status code 500","name":"AxiosError","stack":"AxiosError: Request failed with status code 500\n    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)\n    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)\n    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)\n    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)\n    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)\n    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24)","config":{"transitional":{"silentJSONParsing":true,"forcedJSONParsing":true,"clarifyTimeoutError":false},"adapter":["xhr","http","fetch"],"transformRequest":[null],"transformResponse":[null],"timeout":10000,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Content-Type":"application/json"},"baseURL":"http://localhost:5279/api","method":"post","url":"/workflows","data":"{\u0022id\u0022:\u002200000000-0000-0000-0000-000000000000\u0022,\u0022name\u0022:\u0022\u65B0\u5DE5\u4F5C\u6D41_1753712578142\u0022,\u0022description\u0022:\u0022\u65B0\u521B\u5EFA\u7684\u5DE5\u4F5C\u6D41\u0022,\u0022version\u0022:1,\u0022isEnabled\u0022:true,\u0022createdAt\u0022:\u00222025-07-28T14:22:58.143Z\u0022,\u0022updatedAt\u0022:\u00222025-07-28T14:22:58.143Z\u0022,\u0022createdBy\u0022:\u0022user\u0022,\u0022nodes\u0022:[],\u0022connections\u0022:[],\u0022variables\u0022:{},\u0022configuration\u0022:{\u0022timeoutSeconds\u0022:300,\u0022maxRetryCount\u0022:3,\u0022enableParallelExecution\u0022:true,\u0022errorHandling\u0022:\u0022StopOnError\u0022,\u0022logLevel\u0022:\u0022Information\u0022}}","allowAbsoluteUrls":true},"code":"ERR_BAD_RESPONSE","status":500}},"StackTrace":null}
[2025-07-28 14:22:58.576 +00:00 ERR] FlowCustomV1.Core.Logging.LoggingService: 前端日志: [Session:kvw21fhv] [ERROR] [Console] Failed to create workflow: Error: Request failed with status code 500
AxiosError: Request failed with status code 500
    at settle (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1232:12)
    at XMLHttpRequest.onloadend (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:1564:7)
    at Axios.request (http://localhost:5173/node_modules/.vite/deps/axios.js?v=299b0277:2122:41)
    at async Object.create (http://localhost:5173/src/services/api.ts:69:22)
    at async createWorkflow (http://localhost:5173/src/stores/workflowStore.ts:47:24)
    at async handleCreateWorkflow (http://localhost:5173/src/App.tsx:65:24) URL: http://localhost:5173/ {"Id":"1753712578355-eb75g2j1m","SessionId":"session_1753712575472_ekvw21fhv","UserId":"Unknown","UserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","Url":"http://localhost:5173/","Category":"Console","Data":null,"StackTrace":null}
[2025-07-28 14:25:20.352 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 14:25:20.372 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 14:27:47.996 +00:00 ERR] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE `ParameterTemplates` (
    `Id` TEXT NOT NULL,
    `Name` TEXT NOT NULL,
    `DisplayName` TEXT NOT NULL,
    `Description` TEXT NULL,
    `Category` TEXT NOT NULL,
    `Version` TEXT NOT NULL,
    `IsBuiltIn` INTEGER NOT NULL,
    `IsPublic` INTEGER NOT NULL,
    `Structure` TEXT NOT NULL,
    `Metadata` TEXT NOT NULL,
    `Preview` TEXT NULL,
    CONSTRAINT `PK_ParameterTemplates` PRIMARY KEY (`Id`)
);
[2025-07-28 14:27:48.012 +00:00 ERR] FlowCustomV1.Api.Services.DatabaseInitializationService: ❌ 数据库迁移失败，尝试备用方案...
MySqlConnector.MySqlException (0x80004005): BLOB/TEXT column 'Id' used in key specification without a key length
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 309
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQueryAsync(IEnumerable`1 migrationCommands, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at FlowCustomV1.Api.Services.DatabaseInitializationService.HandleDatabaseMigrationsAsync(FlowCustomDbContext context) in /src/src/FlowCustomV1.Api/Services/DatabaseInitializationService.cs:line 103
[2025-07-28 14:28:26.011 +00:00 ERR] FlowCustomV1.Api.Controllers.WorkflowsController: 创建工作流失败: ?????
System.ArgumentException: 工作流引擎配置无效: The field MaxConcurrentExecutions must be between 1 and 100000., The field ExecutionQueueCapacity must be between 100 and 1000000.
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.Validate() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 112
   at FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance() in /src/src/FlowCustomV1.Engine/Configuration/WorkflowEngineConfiguration.cs:line 190
   at Program.<>c.<<Main>$>b__0_4(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 132
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<>c.<<Main>$>b__0_5(IServiceProvider provider) in /src/src/FlowCustomV1.Api/Program.cs:line 147
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at FlowCustomV1.Api.Controllers.WorkflowsController.GetWorkflowEngine() in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 37
   at FlowCustomV1.Api.Controllers.WorkflowsController.CreateWorkflow(WorkflowDefinition workflow, CancellationToken cancellationToken) in /src/src/FlowCustomV1.Api/Controllers/WorkflowsController.cs:line 120
