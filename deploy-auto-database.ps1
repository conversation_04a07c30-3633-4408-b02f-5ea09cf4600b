#!/usr/bin/env pwsh
# FlowCustomV1 自动数据库检测和部署脚本

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("SQLite", "MySQL", "PostgreSQL", "Auto")]
    [string]$DatabaseType = "Auto",
    
    [Parameter(Mandatory=$false)]
    [switch]$Force,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# 检查Docker是否运行
function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 检查数据库服务是否可用
function Test-DatabaseService {
    param([string]$Type)
    
    switch ($Type) {
        "MySQL" {
            try {
                $result = Test-NetConnection -ComputerName localhost -Port 3306 -WarningAction SilentlyContinue
                return $result.TcpTestSucceeded
            } catch {
                return $false
            }
        }
        "PostgreSQL" {
            try {
                $result = Test-NetConnection -ComputerName localhost -Port 5432 -WarningAction SilentlyContinue
                return $result.TcpTestSucceeded
            } catch {
                return $false
            }
        }
        "SQLite" {
            return $true  # SQLite总是可用的
        }
        default {
            return $false
        }
    }
}

# 自动检测最佳数据库类型
function Get-OptimalDatabaseType {
    Write-Log "🔍 自动检测最佳数据库类型..."
    
    # 检查是否有现有数据
    $hasExistingData = $false
    
    if (Test-Path "data/flowcustom.db") {
        Write-Log "📊 发现现有SQLite数据库"
        $hasExistingData = $true
        return "SQLite"
    }
    
    # 检查可用的数据库服务
    $availableDatabases = @()
    
    if (Test-DatabaseService -Type "PostgreSQL") {
        $availableDatabases += "PostgreSQL"
        Write-Log "✅ PostgreSQL服务可用"
    }
    
    if (Test-DatabaseService -Type "MySQL") {
        $availableDatabases += "MySQL"
        Write-Log "✅ MySQL服务可用"
    }
    
    # 优先级：PostgreSQL > MySQL > SQLite
    if ($availableDatabases -contains "PostgreSQL") {
        Write-Log "🎯 选择PostgreSQL（最佳性能和功能）"
        return "PostgreSQL"
    } elseif ($availableDatabases -contains "MySQL") {
        Write-Log "🎯 选择MySQL（良好的性能和兼容性）"
        return "MySQL"
    } else {
        Write-Log "🎯 选择SQLite（简单部署，无需外部服务）"
        return "SQLite"
    }
}

# 部署指定类型的数据库
function Deploy-Database {
    param([string]$Type)
    
    Write-Log "🚀 部署$Type数据库环境..."
    
    # 停止现有服务
    if (Test-Path "docker-compose.yml") {
        Write-Log "⏹️ 停止现有服务..."
        docker-compose down
    }
    
    # 选择对应的docker-compose文件
    $composeFile = switch ($Type) {
        "MySQL" { "docker-compose.mysql.yml" }
        "PostgreSQL" { "docker-compose.postgresql.yml" }
        "SQLite" { "docker-compose.yml" }
        default { "docker-compose.yml" }
    }
    
    if (!(Test-Path $composeFile)) {
        throw "Docker Compose文件不存在: $composeFile"
    }
    
    Write-Log "📋 使用配置文件: $composeFile"
    
    # 启动服务
    Write-Log "🔄 启动服务..."
    docker-compose -f $composeFile up -d
    
    # 等待服务启动
    Write-Log "⏳ 等待服务启动..."
    Start-Sleep -Seconds 30
    
    # 检查服务状态
    $services = docker-compose -f $composeFile ps --format json | ConvertFrom-Json
    $allHealthy = $true
    
    foreach ($service in $services) {
        if ($service.State -ne "running") {
            Write-Log "❌ 服务 $($service.Service) 状态异常: $($service.State)" -Level "ERROR"
            $allHealthy = $false
        } else {
            Write-Log "✅ 服务 $($service.Service) 运行正常"
        }
    }
    
    if (!$allHealthy) {
        throw "部分服务启动失败"
    }
    
    # 测试API连接
    Write-Log "🔗 测试API连接..."
    $maxRetries = 10
    $retryCount = 0
    
    do {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5279/api/plugin/node-types/by-category" -Method GET -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Log "✅ API连接测试成功" -Level "SUCCESS"
                break
            }
        } catch {
            $retryCount++
            if ($retryCount -lt $maxRetries) {
                Write-Log "⏳ API连接测试失败，重试 $retryCount/$maxRetries..."
                Start-Sleep -Seconds 5
            } else {
                Write-Log "❌ API连接测试失败" -Level "ERROR"
                throw "API服务无法访问"
            }
        }
    } while ($retryCount -lt $maxRetries)
    
    Write-Log "🎉 $Type数据库环境部署成功！" -Level "SUCCESS"
}

# 主函数
function Main {
    try {
        Write-Log "🚀 FlowCustomV1 自动数据库部署开始..."
        
        # 检查Docker
        if (!(Test-DockerRunning)) {
            throw "Docker未运行，请先启动Docker"
        }
        
        # 确定数据库类型
        $selectedType = if ($DatabaseType -eq "Auto") {
            Get-OptimalDatabaseType
        } else {
            $DatabaseType
        }
        
        Write-Log "📊 选择的数据库类型: $selectedType"
        
        # 部署数据库
        Deploy-Database -Type $selectedType
        
        # 显示访问信息
        Write-Log "🌐 系统访问信息:" -Level "SUCCESS"
        Write-Log "   前端: http://localhost:5173" -Level "SUCCESS"
        Write-Log "   后端API: http://localhost:5279" -Level "SUCCESS"
        Write-Log "   NATS监控: http://localhost:8222" -Level "SUCCESS"
        Write-Log "   数据库类型: $selectedType" -Level "SUCCESS"
        
        if ($selectedType -eq "MySQL") {
            Write-Log "   MySQL: localhost:3306 (用户: flowcustom)" -Level "SUCCESS"
        } elseif ($selectedType -eq "PostgreSQL") {
            Write-Log "   PostgreSQL: localhost:5432 (用户: flowcustom)" -Level "SUCCESS"
        }
        
    } catch {
        Write-Log "❌ 部署失败: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

# 执行主函数
Main
