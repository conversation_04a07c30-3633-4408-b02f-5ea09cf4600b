using FlowCustomV1.Api.Services;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace FlowCustomV1.Api.Services
{
    /// <summary>
    /// 后端日志NATS发布服务
    /// </summary>
    public interface IBackendLogNatsPublisher
    {
        /// <summary>
        /// 发布后端日志到NATS
        /// </summary>
        Task PublishLogAsync(string level, string category, string message, object? data = null, string? exception = null);
    }

    /// <summary>
    /// 后端日志NATS发布服务实现
    /// </summary>
    public class BackendLogNatsPublisher : IBackendLogNatsPublisher
    {
        private readonly INatsService _natsService;
        private readonly ILogger<BackendLogNatsPublisher> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public BackendLogNatsPublisher(INatsService natsService, ILogger<BackendLogNatsPublisher> logger)
        {
            _natsService = natsService;
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        public async Task PublishLogAsync(string level, string category, string message, object? data = null, string? exception = null)
        {
            try
            {
                // 只发布重要级别的日志到NATS，避免网络拥塞
                if (!ShouldPublishLevel(level))
                {
                    return;
                }

                var logMessage = new
                {
                    type = "backend_log",
                    timestamp = DateTime.UtcNow.ToString("O"),
                    level = level,
                    category = category,
                    message = message,
                    data = data,
                    exception = exception,
                    source = "backend",
                    machineName = Environment.MachineName,
                    processId = Environment.ProcessId
                };

                var subject = $"system.log.backend.{level.ToLower()}";
                await _natsService.PublishLogMessageAsync(level, category, message, logMessage);
            }
            catch (Exception ex)
            {
                // 避免日志循环，使用内部日志记录
                _logger.LogError(ex, "发布后端日志到NATS失败: {Level} {Category} {Message}", level, category, message);
            }
        }

        /// <summary>
        /// 判断是否应该发布该级别的日志
        /// </summary>
        private static bool ShouldPublishLevel(string level)
        {
            return level.ToUpper() switch
            {
                "WARNING" or "WARN" => true,
                "ERROR" => true,
                "CRITICAL" => true,
                "FATAL" => true,
                _ => false // 不发布 DEBUG, INFO, TRACE 级别的日志
            };
        }
    }
}
