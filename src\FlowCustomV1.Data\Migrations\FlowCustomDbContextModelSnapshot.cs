﻿// <auto-generated />
using System;
using FlowCustomV1.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace FlowCustomV1.Data.Migrations
{
    [DbContext(typeof(FlowCustomDbContext))]
    partial class FlowCustomDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .UseCollation("utf8mb4_unicode_ci")
                .HasAnnotation("ProductVersion", "8.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.HasCharSet(modelBuilder, "utf8mb4");
            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("FlowCustomV1.Core.Models.ExecutionLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Details")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Exception")
                        .HasColumnType("varchar(255)");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<Guid?>("NodeExecutionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Source")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("WorkflowExecutionId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("Level");

                    b.HasIndex("NodeExecutionId");

                    b.HasIndex("Timestamp");

                    b.HasIndex("WorkflowExecutionId");

                    b.ToTable("executionlogs");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.NodeExecution", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ErrorDetails")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("InputData")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("NodeId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NodeName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NodeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("OutputData")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("RetryCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("WorkflowExecutionId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("CompletedAt");

                    b.HasIndex("NodeId");

                    b.HasIndex("StartedAt");

                    b.HasIndex("Status");

                    b.HasIndex("WorkflowExecutionId");

                    b.ToTable("nodeexecutions");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.ParameterTemplate", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsBuiltIn")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Preview")
                        .HasColumnType("longtext");

                    b.Property<string>("Structure")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("Version");

                    b.ToTable("parametertemplates");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.PluginDefinition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("AssemblyName")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Author")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Category")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ConfigurationSchema")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Dependencies")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Icon")
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSystemPlugin")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastLoadedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LoadError")
                        .HasColumnType("varchar(255)");

                    b.Property<int>("LoadStatus")
                        .HasColumnType("int");

                    b.Property<string>("MainTypeName")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("NodeTypes")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Tags")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsEnabled");

                    b.HasIndex("LoadStatus");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("plugindefinitions");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.TemplateCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsBuiltIn")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("Order");

                    b.ToTable("templatecategories");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.TemplateDefinition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ApplicableNodeTypes")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("DefaultParameters")
                        .HasColumnType("longtext");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Fields")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("IsBuiltIn")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OutputSchema")
                        .HasColumnType("longtext");

                    b.Property<string>("Tags")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("UsageCount")
                        .HasColumnType("int");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsEnabled");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("Type");

                    b.HasIndex("UsageCount");

                    b.ToTable("templatedefinitions");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.TemplateInstance", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime>("AppliedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("AppliedBy")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ParameterValues")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("TemplateId")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<string>("TemplateVersion")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("AppliedAt");

                    b.HasIndex("AppliedBy");

                    b.HasIndex("TemplateId");

                    b.ToTable("templateinstances");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.TemplateUsageStatistics", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("NodeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Success")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("TemplateId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("UsedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UsedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("WorkflowId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("NodeType");

                    b.HasIndex("TemplateId");

                    b.HasIndex("UsedAt");

                    b.HasIndex("UsedBy");

                    b.ToTable("templateusagestatistics");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.WorkflowDefinition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Configuration")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Connections")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Nodes")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Variables")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.Property<string>("VersionDescription")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("VersionTag")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsEnabled");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("workflowdefinitions");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.WorkflowExecution", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Context")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ErrorDetails")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("InputData")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("OutputData")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TriggeredBy")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid>("WorkflowDefinitionId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("CompletedAt");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("StartedAt");

                    b.HasIndex("Status");

                    b.HasIndex("WorkflowDefinitionId");

                    b.ToTable("workflowexecutions");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.WorkflowVersion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ChangeSummary")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("ChangeType")
                        .HasColumnType("int");

                    b.Property<string>("ConfigurationChanges")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ConnectionChanges")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsCurrent")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("NodeChanges")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid?>("ParentVersionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Tags")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.Property<string>("VersionTag")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<Guid>("WorkflowId")
                        .HasColumnType("char(36)");

                    b.Property<string>("WorkflowSnapshot")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("VersionTag");

                    b.HasIndex("WorkflowId");

                    b.HasIndex("WorkflowId", "IsCurrent");

                    b.HasIndex("WorkflowId", "IsPublished");

                    b.HasIndex("WorkflowId", "Version")
                        .IsUnique();

                    b.ToTable("workflowversions");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.ExecutionLog", b =>
                {
                    b.HasOne("FlowCustomV1.Core.Models.WorkflowExecution", null)
                        .WithMany("Logs")
                        .HasForeignKey("WorkflowExecutionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.NodeExecution", b =>
                {
                    b.HasOne("FlowCustomV1.Core.Models.WorkflowExecution", null)
                        .WithMany("NodeExecutions")
                        .HasForeignKey("WorkflowExecutionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.TemplateInstance", b =>
                {
                    b.HasOne("FlowCustomV1.Core.Models.ParameterTemplate", null)
                        .WithMany()
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.TemplateUsageStatistics", b =>
                {
                    b.HasOne("FlowCustomV1.Core.Models.TemplateDefinition", "Template")
                        .WithMany()
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Template");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.WorkflowExecution", b =>
                {
                    b.HasOne("FlowCustomV1.Core.Models.WorkflowDefinition", "WorkflowDefinition")
                        .WithMany()
                        .HasForeignKey("WorkflowDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.WorkflowVersion", b =>
                {
                    b.HasOne("FlowCustomV1.Core.Models.WorkflowDefinition", null)
                        .WithMany()
                        .HasForeignKey("WorkflowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("FlowCustomV1.Core.Models.WorkflowExecution", b =>
                {
                    b.Navigation("Logs");

                    b.Navigation("NodeExecutions");
                });
#pragma warning restore 612, 618
        }
    }
}
