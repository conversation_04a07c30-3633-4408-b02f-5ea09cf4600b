# NATS Server Configuration

# Basic server settings
port: 4222
http_port: 8222

# Enable JetStream
jetstream {
    store_dir: "/data"
}

# WebSocket configuration
websocket {
    port: 8080
    no_tls: true

    # Enable compression for better performance
    compression: true

    # Handshake timeout
    handshake_timeout: "5s"

    # Allow all origins for development (in production, restrict this)
    same_origin: false
}

# Logging
debug: false
trace: false
logtime: true

# Client settings
max_connections: 64K
max_subscriptions: 0
max_payload: 1MB

# Ping settings
ping_interval: "2m"
ping_max: 2

# Write deadline
write_deadline: "10s"
