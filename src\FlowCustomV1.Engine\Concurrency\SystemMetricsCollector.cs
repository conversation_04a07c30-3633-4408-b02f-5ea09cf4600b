using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.Concurrency
{
    /// <summary>
    /// 系统指标收集器 - 收集CPU、内存、队列等系统性能指标
    /// 提供实时的系统负载信息用于智能并发控制
    /// </summary>
    public class SystemMetricsCollector : IDisposable
    {
        private readonly ILogger _logger;
        private readonly PerformanceCounter? _cpuCounter;
        private readonly Process _currentProcess;
        private readonly Timer _metricsUpdateTimer;
        private volatile SystemMetrics _currentMetrics;
        private volatile bool _disposed = false;

        // 指标统计
        private long _totalRequests;
        private long _totalErrors;
        private long _totalResponseTime;
        private readonly object _metricsLock = new object();

        // CPU使用率计算缓存
        private DateTime? _lastCpuTime;
        private TimeSpan? _lastProcessorTime;

        /// <summary>
        /// 初始化系统指标收集器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public SystemMetricsCollector(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _currentProcess = Process.GetCurrentProcess();

            try
            {
                // 只在Windows上初始化CPU性能计数器
                if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                {
                    _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                    _cpuCounter.NextValue(); // 第一次调用返回0，需要预热
                    _logger.LogDebug("Windows CPU性能计数器初始化成功");
                }
                else
                {
                    _logger.LogDebug("非Windows平台，跳过PerformanceCounter初始化，将使用替代方法");
                    _cpuCounter = null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法初始化CPU性能计数器，将使用替代方法");
                _cpuCounter = null;
            }

            // 初始化指标
            _currentMetrics = new SystemMetrics
            {
                CpuUsage = 0.0,
                MemoryUsage = 0.0,
                QueueLength = 0,
                AverageResponseTimeMs = 0.0,
                ErrorRate = 0.0,
                Timestamp = DateTime.UtcNow
            };

            // 启动定期更新定时器
            _metricsUpdateTimer = new Timer(UpdateMetrics!, null, TimeSpan.Zero, TimeSpan.FromSeconds(1));

            _logger.LogDebug("SystemMetricsCollector initialized");
        }

        /// <summary>
        /// 获取当前系统指标
        /// </summary>
        /// <returns>系统指标</returns>
        public SystemMetrics GetCurrentMetrics()
        {
            return _currentMetrics;
        }

        /// <summary>
        /// 记录请求开始
        /// </summary>
        public void RecordRequestStart()
        {
            Interlocked.Increment(ref _totalRequests);
        }

        /// <summary>
        /// 记录请求完成
        /// </summary>
        /// <param name="responseTimeMs">响应时间（毫秒）</param>
        /// <param name="isError">是否为错误</param>
        public void RecordRequestComplete(double responseTimeMs, bool isError = false)
        {
            if (isError)
            {
                Interlocked.Increment(ref _totalErrors);
            }

            lock (_metricsLock)
            {
                _totalResponseTime += (long)responseTimeMs;
            }
        }

        /// <summary>
        /// 设置当前队列长度
        /// </summary>
        /// <param name="queueLength">队列长度</param>
        public void SetQueueLength(int queueLength)
        {
            var currentMetrics = _currentMetrics;
            _currentMetrics = new SystemMetrics
            {
                CpuUsage = currentMetrics.CpuUsage,
                MemoryUsage = currentMetrics.MemoryUsage,
                QueueLength = queueLength,
                AverageResponseTimeMs = currentMetrics.AverageResponseTimeMs,
                ErrorRate = currentMetrics.ErrorRate,
                Timestamp = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 更新系统指标
        /// </summary>
        private void UpdateMetrics(object state)
        {
            try
            {
                if (_disposed) return;

                var cpuUsage = GetCpuUsage();
                var memoryUsage = GetMemoryUsage();
                var (avgResponseTime, errorRate) = GetPerformanceMetrics();

                _currentMetrics = new SystemMetrics
                {
                    CpuUsage = cpuUsage,
                    MemoryUsage = memoryUsage,
                    QueueLength = _currentMetrics.QueueLength, // 保持当前队列长度
                    AverageResponseTimeMs = avgResponseTime,
                    ErrorRate = errorRate,
                    Timestamp = DateTime.UtcNow
                };

                _logger.LogTrace("系统指标更新: CPU={CpuUsage:P}, Memory={MemoryUsage:P}, Queue={QueueLength}, " +
                               "AvgResponse={AvgResponseTime:F2}ms, ErrorRate={ErrorRate:P}",
                    cpuUsage, memoryUsage, _currentMetrics.QueueLength, avgResponseTime, errorRate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新系统指标时发生错误");
            }
        }

        /// <summary>
        /// 获取CPU使用率
        /// </summary>
        /// <returns>CPU使用率 (0.0-1.0)</returns>
        private double GetCpuUsage()
        {
            try
            {
                if (_cpuCounter != null)
                {
                    // Windows环境使用PerformanceCounter
                    var cpuUsage = _cpuCounter.NextValue() / 100.0;
                    return Math.Max(0.0, Math.Min(1.0, cpuUsage));
                }
                else
                {
                    // 跨平台方法：使用进程CPU时间
                    return GetCrossplatformCpuUsage();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取CPU使用率失败，返回默认值");
                return 0.3; // 返回较低负载作为默认值
            }
        }

        /// <summary>
        /// 获取跨平台CPU使用率
        /// </summary>
        /// <returns>CPU使用率 (0.0-1.0)</returns>
        private double GetCrossplatformCpuUsage()
        {
            try
            {
                // 使用进程CPU时间计算使用率
                var currentTime = DateTime.UtcNow;
                var currentProcessorTime = _currentProcess.TotalProcessorTime;

                if (_lastCpuTime.HasValue && _lastProcessorTime.HasValue)
                {
                    var timeDiff = currentTime - _lastCpuTime.Value;
                    var processorTimeDiff = currentProcessorTime - _lastProcessorTime.Value;

                    if (timeDiff.TotalMilliseconds > 0)
                    {
                        var cpuUsage = processorTimeDiff.TotalMilliseconds / timeDiff.TotalMilliseconds / Environment.ProcessorCount;

                        // 更新缓存值
                        _lastCpuTime = currentTime;
                        _lastProcessorTime = currentProcessorTime;

                        return Math.Max(0.0, Math.Min(1.0, cpuUsage));
                    }
                }

                // 初始化或更新缓存值
                _lastCpuTime = currentTime;
                _lastProcessorTime = currentProcessorTime;

                // 首次调用返回较低的默认值
                return 0.1;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取跨平台CPU使用率失败");
                return 0.2;
            }
        }

        /// <summary>
        /// 获取内存使用率
        /// </summary>
        /// <returns>内存使用率 (0.0-1.0)</returns>
        private double GetMemoryUsage()
        {
            try
            {
                var workingSet = _currentProcess.WorkingSet64;
                var totalMemory = GC.GetTotalMemory(false);
                
                // 使用工作集内存作为基准
                var availableMemory = GetAvailablePhysicalMemory();
                if (availableMemory > 0)
                {
                    var memoryUsage = (double)workingSet / availableMemory;
                    return Math.Max(0.0, Math.Min(1.0, memoryUsage));
                }
                else
                {
                    // 如果无法获取系统内存信息，使用GC内存作为估算
                    var estimatedMaxMemory = 2L * 1024 * 1024 * 1024; // 假设2GB最大内存
                    var memoryUsage = (double)totalMemory / estimatedMaxMemory;
                    return Math.Max(0.0, Math.Min(1.0, memoryUsage));
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取内存使用率失败，返回默认值");
                return 0.3; // 返回较低负载作为默认值
            }
        }

        /// <summary>
        /// 获取可用物理内存
        /// </summary>
        /// <returns>可用物理内存字节数</returns>
        private long GetAvailablePhysicalMemory()
        {
            try
            {
                // 在Windows上使用性能计数器
                if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                {
                    using var counter = new PerformanceCounter("Memory", "Available Bytes");
                    return (long)counter.NextValue();
                }
                else
                {
                    // 在Linux/Unix上读取/proc/meminfo
                    return GetLinuxMemoryInfo();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取物理内存信息失败");
                return 0; // 无法获取时返回0
            }
        }

        /// <summary>
        /// 从Linux /proc/meminfo获取内存信息
        /// </summary>
        /// <returns>可用内存字节数</returns>
        private long GetLinuxMemoryInfo()
        {
            try
            {
                if (!File.Exists("/proc/meminfo"))
                {
                    // 如果不是Linux系统，返回估算值
                    return 8L * 1024 * 1024 * 1024; // 假设8GB可用内存
                }

                var lines = File.ReadAllLines("/proc/meminfo");
                long totalMemory = 0;
                long availableMemory = 0;

                foreach (var line in lines)
                {
                    if (line.StartsWith("MemTotal:"))
                    {
                        var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 2 && long.TryParse(parts[1], out var value))
                        {
                            totalMemory = value * 1024; // 转换为字节
                        }
                    }
                    else if (line.StartsWith("MemAvailable:"))
                    {
                        var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 2 && long.TryParse(parts[1], out var value))
                        {
                            availableMemory = value * 1024; // 转换为字节
                        }
                    }
                }

                // 如果找到了MemAvailable，使用它；否则使用MemTotal的80%作为估算
                return availableMemory > 0 ? availableMemory : (long)(totalMemory * 0.8);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "读取Linux内存信息失败");
                return 8L * 1024 * 1024 * 1024; // 返回默认值8GB
            }
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        /// <returns>平均响应时间和错误率</returns>
        private (double avgResponseTime, double errorRate) GetPerformanceMetrics()
        {
            lock (_metricsLock)
            {
                var totalRequests = _totalRequests;
                var totalErrors = _totalErrors;
                var totalResponseTime = _totalResponseTime;

                if (totalRequests == 0)
                {
                    return (0.0, 0.0);
                }

                var avgResponseTime = (double)totalResponseTime / totalRequests;
                var errorRate = (double)totalErrors / totalRequests;

                return (avgResponseTime, errorRate);
            }
        }

        /// <summary>
        /// 重置性能统计
        /// </summary>
        public void ResetPerformanceStats()
        {
            lock (_metricsLock)
            {
                _totalRequests = 0;
                _totalErrors = 0;
                _totalResponseTime = 0;
            }

            _logger.LogDebug("性能统计已重置");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _metricsUpdateTimer?.Dispose();
                _cpuCounter?.Dispose();
                _currentProcess?.Dispose();
                _logger.LogDebug("SystemMetricsCollector disposed");
            }
        }
    }
}
