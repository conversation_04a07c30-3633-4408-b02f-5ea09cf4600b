using Microsoft.EntityFrameworkCore;
using Npgsql;
using FlowCustomV1.Core.Models;

namespace FlowCustomV1.Data.Providers;

/// <summary>
/// PostgreSQL数据库提供者
/// </summary>
public class PostgreSqlProvider : IDatabaseProvider
{
    /// <inheritdoc />
    public string ProviderName => "PostgreSQL";

    /// <inheritdoc />
    public void ConfigureDbContext(DbContextOptionsBuilder builder, string connectionString)
    {
        builder.UseNpgsql(connectionString, options =>
        {
            options.MigrationsAssembly(GetMigrationAssembly());
            options.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(5),
                errorCodesToAdd: null);
        });
    }

    /// <inheritdoc />
    public string GetMigrationAssembly()
    {
        return "FlowCustomV1.Data";
    }

    /// <inheritdoc />
    public void ConfigureModelBuilder(ModelBuilder modelBuilder)
    {
        // PostgreSQL特定配置
        
        // 配置字符串长度限制
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(string))
                {
                    var maxLength = property.GetMaxLength();
                    if (!maxLength.HasValue)
                    {
                        // 为没有明确长度限制的字符串设置默认长度
                        property.SetMaxLength(2000);
                    }
                }
            }
        }

        // 配置PostgreSQL特定的索引和约束
        ConfigurePostgreSqlSpecificConstraints(modelBuilder);
    }

    /// <summary>
    /// 配置PostgreSQL特定的约束和索引
    /// </summary>
    private void ConfigurePostgreSqlSpecificConstraints(ModelBuilder modelBuilder)
    {
        // 工作流表的PostgreSQL特定配置
        modelBuilder.Entity<WorkflowDefinition>(entity =>
        {
            entity.HasIndex(e => e.Name)
                .HasDatabaseName("IX_WorkflowDefinitions_Name_PostgreSQL");

            entity.HasIndex(e => e.CreatedAt)
                .HasDatabaseName("IX_WorkflowDefinitions_CreatedAt_PostgreSQL");
        });

        // 工作流执行表的PostgreSQL特定配置
        modelBuilder.Entity<WorkflowExecution>(entity =>
        {
            entity.HasIndex(e => e.StartedAt)
                .HasDatabaseName("IX_WorkflowExecutions_StartedAt_PostgreSQL");

            entity.HasIndex(e => new { e.WorkflowDefinitionId, e.Status })
                .HasDatabaseName("IX_WorkflowExecutions_WorkflowDefinitionId_Status_PostgreSQL");
        });
    }

    /// <inheritdoc />
    public string GetDatabaseSpecificSql(string sqlType)
    {
        return sqlType switch
        {
            "GetUtcNow" => "NOW() AT TIME ZONE 'UTC'",
            "GetRandomId" => "gen_random_uuid()::text",
            "CascadeDelete" => "-- PostgreSQL supports CASCADE by default",
            "DisableForeignKeyChecks" => "SET session_replication_role = replica",
            "EnableForeignKeyChecks" => "SET session_replication_role = DEFAULT",
            _ => throw new NotSupportedException($"SQL type '{sqlType}' is not supported for PostgreSQL")
        };
    }

    /// <inheritdoc />
    public bool ValidateConnectionString(string connectionString)
    {
        try
        {
            var connectionStringBuilder = new NpgsqlConnectionStringBuilder(connectionString);
            return !string.IsNullOrEmpty(connectionStringBuilder.Host) && 
                   !string.IsNullOrEmpty(connectionStringBuilder.Database);
        }
        catch
        {
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> TestConnectionAsync(string connectionString)
    {
        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            await connection.CloseAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DatabaseExistsAsync(string connectionString)
    {
        try
        {
            var builder = new NpgsqlConnectionStringBuilder(connectionString);
            var databaseName = builder.Database;
            
            // 连接到postgres系统数据库检查目标数据库是否存在
            builder.Database = "postgres";
            var systemConnectionString = builder.ToString();
            
            using var connection = new NpgsqlConnection(systemConnectionString);
            await connection.OpenAsync();
            
            using var command = connection.CreateCommand();
            command.CommandText = "SELECT 1 FROM pg_database WHERE datname = @databaseName";
            command.Parameters.AddWithValue("@databaseName", databaseName ?? string.Empty);

            var result = await command.ExecuteScalarAsync();
            return result != null;
        }
        catch
        {
            return false;
        }
    }

    /// <inheritdoc />
    public async Task CreateDatabaseIfNotExistsAsync(string connectionString)
    {
        var builder = new NpgsqlConnectionStringBuilder(connectionString);
        var databaseName = builder.Database;
        
        // 连接到postgres系统数据库
        builder.Database = "postgres";
        var systemConnectionString = builder.ToString();
        
        using var connection = new NpgsqlConnection(systemConnectionString);
        await connection.OpenAsync();
        
        // 检查数据库是否存在
        using var checkCommand = connection.CreateCommand();
        checkCommand.CommandText = "SELECT 1 FROM pg_database WHERE datname = @databaseName";
        checkCommand.Parameters.AddWithValue("@databaseName", databaseName ?? string.Empty);
        
        var exists = await checkCommand.ExecuteScalarAsync();
        if (exists == null)
        {
            // 创建数据库
            using var createCommand = connection.CreateCommand();
            createCommand.CommandText = $"CREATE DATABASE \"{databaseName}\" WITH ENCODING 'UTF8'";
            await createCommand.ExecuteNonQueryAsync();
        }
    }

    /// <inheritdoc />
    public string GetConnectionStringTemplate()
    {
        return "Host=localhost;Port=5432;Database=flowcustom;Username=flowcustom;Password=FlowCustom@2025;";
    }

    /// <inheritdoc />
    public Dictionary<string, string> GetDefaultConfiguration()
    {
        return new Dictionary<string, string>
        {
            ["Host"] = "localhost",
            ["Port"] = "5432",
            ["Database"] = "flowcustom",
            ["Username"] = "flowcustom",
            ["Password"] = "FlowCustom@2025",
            ["Pooling"] = "true",
            ["MinPoolSize"] = "1",
            ["MaxPoolSize"] = "20",
            ["ConnectionLifetime"] = "300",
            ["CommandTimeout"] = "30"
        };
    }
}
