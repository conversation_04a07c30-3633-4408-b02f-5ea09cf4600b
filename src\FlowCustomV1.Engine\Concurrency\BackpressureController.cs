using System;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Models;

namespace FlowCustomV1.Engine.Concurrency
{
    /// <summary>
    /// 背压控制器 - 监控队列长度并实现智能背压机制
    /// 当系统负载过高时自动触发背压控制，保护系统稳定性
    /// 集成到WorkflowEngine中提供全面的流量控制
    /// </summary>
    public class BackpressureController : IDisposable
    {
        private readonly ILogger<BackpressureController> _logger;
        private readonly BackpressureSettings _settings;
        private readonly Timer? _monitoringTimer;
        private readonly object _stateLock = new object();
        
        // 背压状态
        private volatile BackpressureState _currentState = BackpressureState.Normal;
        private DateTime _lastStateChange = DateTime.UtcNow;
        private volatile int _consecutiveHighLoadCount = 0;
        private volatile int _consecutiveNormalLoadCount = 0;
        
        // 监控指标
        private volatile BackpressureMetrics _currentMetrics;
        private bool _disposed = false;

        /// <summary>
        /// 当前背压状态
        /// </summary>
        public BackpressureState CurrentState => _currentState;

        /// <summary>
        /// 当前背压指标
        /// </summary>
        public BackpressureMetrics CurrentMetrics => _currentMetrics;

        /// <summary>
        /// 背压状态变化事件
        /// </summary>
        public event EventHandler<BackpressureStateChangedEventArgs>? StateChanged;

        /// <summary>
        /// 初始化背压控制器
        /// </summary>
        /// <param name="settings">背压控制设置</param>
        /// <param name="logger">日志记录器</param>
        public BackpressureController(BackpressureSettings settings, ILogger<BackpressureController> logger)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 验证设置
            _settings.Validate();

            // 初始化指标
            _currentMetrics = new BackpressureMetrics
            {
                QueueLength = 0,
                QueueCapacity = 0,
                QueueUtilization = 0.0,
                ProcessingRate = 0.0,
                AverageWaitTime = TimeSpan.Zero,
                State = BackpressureState.Normal,
                Timestamp = DateTime.UtcNow
            };

            // 启动监控定时器
            if (_settings.EnableMonitoring)
            {
                _monitoringTimer = new Timer(
                    MonitorBackpressure!,
                    null,
                    TimeSpan.FromSeconds(_settings.MonitoringIntervalSeconds),
                    TimeSpan.FromSeconds(_settings.MonitoringIntervalSeconds));
            }

            _logger.LogInformation("BackpressureController initialized with settings: {Settings}", _settings);
        }

        /// <summary>
        /// 检查是否应该应用背压
        /// </summary>
        /// <param name="queueLength">当前队列长度</param>
        /// <param name="queueCapacity">队列容量</param>
        /// <returns>是否应该应用背压</returns>
        public bool ShouldApplyBackpressure(int queueLength, int queueCapacity)
        {
            ThrowIfDisposed();

            // 更新当前指标
            UpdateMetrics(queueLength, queueCapacity);

            // 根据当前状态和阈值判断
            var utilization = queueCapacity > 0 ? (double)queueLength / queueCapacity : 0.0;
            
            switch (_currentState)
            {
                case BackpressureState.Normal:
                    return utilization >= _settings.HighWatermark;
                    
                case BackpressureState.Warning:
                    return utilization >= _settings.CriticalWatermark;
                    
                case BackpressureState.Critical:
                    return true; // 在关键状态下始终应用背压
                    
                default:
                    return false;
            }
        }

        /// <summary>
        /// 计算背压延迟时间
        /// </summary>
        /// <param name="queueLength">当前队列长度</param>
        /// <param name="queueCapacity">队列容量</param>
        /// <returns>建议的延迟时间</returns>
        public TimeSpan CalculateBackpressureDelay(int queueLength, int queueCapacity)
        {
            ThrowIfDisposed();

            if (!ShouldApplyBackpressure(queueLength, queueCapacity))
            {
                return TimeSpan.Zero;
            }

            var utilization = queueCapacity > 0 ? (double)queueLength / queueCapacity : 0.0;
            
            // 根据利用率计算延迟时间
            var baseDelay = _settings.BaseBackpressureDelayMs;
            var maxDelay = _settings.MaxBackpressureDelayMs;
            
            // 使用指数函数计算延迟，确保在高负载时快速增加延迟
            var delayMultiplier = Math.Pow(utilization, 2); // 平方函数，使延迟增长更快
            var calculatedDelay = (int)(baseDelay * delayMultiplier);
            
            // 限制在最大延迟范围内
            var finalDelay = Math.Min(calculatedDelay, maxDelay);
            
            _logger.LogDebug("计算背压延迟: 队列利用率={Utilization:P}, 延迟={Delay}ms", 
                utilization, finalDelay);
            
            return TimeSpan.FromMilliseconds(finalDelay);
        }

        /// <summary>
        /// 应用背压延迟
        /// </summary>
        /// <param name="queueLength">当前队列长度</param>
        /// <param name="queueCapacity">队列容量</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>延迟任务</returns>
        public async Task ApplyBackpressureAsync(int queueLength, int queueCapacity, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            var delay = CalculateBackpressureDelay(queueLength, queueCapacity);
            
            if (delay > TimeSpan.Zero)
            {
                _logger.LogDebug("应用背压延迟: {Delay}ms, 队列长度: {QueueLength}/{QueueCapacity}", 
                    delay.TotalMilliseconds, queueLength, queueCapacity);
                
                await Task.Delay(delay, cancellationToken);
            }
        }

        /// <summary>
        /// 获取当前背压状态
        /// </summary>
        /// <returns>当前背压状态</returns>
        public BackpressureState GetCurrentState()
        {
            return _currentState;
        }

        /// <summary>
        /// 获取背压控制器状态信息
        /// </summary>
        /// <returns>背压控制器状态</returns>
        public BackpressureControllerStatus GetStatus()
        {
            return new BackpressureControllerStatus
            {
                CurrentState = _currentState,
                LastStateChange = _lastStateChange,
                ConsecutiveHighLoadCount = _consecutiveHighLoadCount,
                ConsecutiveNormalLoadCount = _consecutiveNormalLoadCount,
                CurrentMetrics = _currentMetrics,
                Settings = _settings,
                IsMonitoringEnabled = _settings.EnableMonitoring
            };
        }

        /// <summary>
        /// 手动触发状态检查
        /// </summary>
        /// <param name="queueLength">当前队列长度</param>
        /// <param name="queueCapacity">队列容量</param>
        public void CheckState(int queueLength, int queueCapacity)
        {
            ThrowIfDisposed();
            UpdateMetrics(queueLength, queueCapacity);
            EvaluateStateTransition();
        }

        /// <summary>
        /// 更新指标
        /// </summary>
        /// <param name="queueLength">队列长度</param>
        /// <param name="queueCapacity">队列容量</param>
        private void UpdateMetrics(int queueLength, int queueCapacity)
        {
            var utilization = queueCapacity > 0 ? (double)queueLength / queueCapacity : 0.0;
            
            _currentMetrics = new BackpressureMetrics
            {
                QueueLength = queueLength,
                QueueCapacity = queueCapacity,
                QueueUtilization = utilization,
                ProcessingRate = _currentMetrics.ProcessingRate, // 保持当前处理速率
                AverageWaitTime = _currentMetrics.AverageWaitTime, // 保持当前等待时间
                State = _currentState,
                Timestamp = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 监控背压状态
        /// </summary>
        private void MonitorBackpressure(object state)
        {
            try
            {
                if (_disposed) return;

                EvaluateStateTransition();
                
                // 记录监控信息
                if (_currentState != BackpressureState.Normal)
                {
                    _logger.LogInformation("背压监控: 状态={State}, 队列利用率={Utilization:P}, 连续高负载次数={HighLoadCount}",
                        _currentState, _currentMetrics.QueueUtilization, _consecutiveHighLoadCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "背压监控时发生错误");
            }
        }

        /// <summary>
        /// 评估状态转换
        /// </summary>
        private void EvaluateStateTransition()
        {
            lock (_stateLock)
            {
                var utilization = _currentMetrics.QueueUtilization;
                var newState = _currentState;

                // 状态转换逻辑
                switch (_currentState)
                {
                    case BackpressureState.Normal:
                        if (utilization >= _settings.CriticalWatermark)
                        {
                            _consecutiveHighLoadCount++;
                            if (_consecutiveHighLoadCount >= _settings.StateTransitionThreshold)
                            {
                                newState = BackpressureState.Critical;
                            }
                        }
                        else if (utilization >= _settings.HighWatermark)
                        {
                            _consecutiveHighLoadCount++;
                            if (_consecutiveHighLoadCount >= _settings.StateTransitionThreshold)
                            {
                                newState = BackpressureState.Warning;
                            }
                        }
                        else
                        {
                            _consecutiveHighLoadCount = 0;
                        }
                        break;

                    case BackpressureState.Warning:
                        if (utilization >= _settings.CriticalWatermark)
                        {
                            _consecutiveHighLoadCount++;
                            if (_consecutiveHighLoadCount >= _settings.StateTransitionThreshold)
                            {
                                newState = BackpressureState.Critical;
                            }
                        }
                        else if (utilization < _settings.LowWatermark)
                        {
                            _consecutiveNormalLoadCount++;
                            if (_consecutiveNormalLoadCount >= _settings.StateTransitionThreshold)
                            {
                                newState = BackpressureState.Normal;
                            }
                        }
                        else
                        {
                            _consecutiveHighLoadCount = 0;
                            _consecutiveNormalLoadCount = 0;
                        }
                        break;

                    case BackpressureState.Critical:
                        if (utilization < _settings.LowWatermark)
                        {
                            _consecutiveNormalLoadCount++;
                            if (_consecutiveNormalLoadCount >= _settings.StateTransitionThreshold)
                            {
                                newState = BackpressureState.Warning;
                            }
                        }
                        else
                        {
                            _consecutiveNormalLoadCount = 0;
                        }
                        break;
                }

                // 执行状态转换
                if (newState != _currentState)
                {
                    TransitionToState(newState);
                }
            }
        }

        /// <summary>
        /// 转换到新状态
        /// </summary>
        /// <param name="newState">新状态</param>
        private void TransitionToState(BackpressureState newState)
        {
            var oldState = _currentState;
            _currentState = newState;
            _lastStateChange = DateTime.UtcNow;
            _consecutiveHighLoadCount = 0;
            _consecutiveNormalLoadCount = 0;

            _logger.LogInformation("背压状态转换: {OldState} -> {NewState}, 队列利用率: {Utilization:P}",
                oldState, newState, _currentMetrics.QueueUtilization);

            // 触发状态变化事件
            StateChanged?.Invoke(this, new BackpressureStateChangedEventArgs
            {
                OldState = oldState,
                NewState = newState,
                Timestamp = _lastStateChange,
                Metrics = _currentMetrics
            });
        }

        /// <summary>
        /// 检查是否已释放
        /// </summary>
        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(BackpressureController));
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _monitoringTimer?.Dispose();
                _disposed = true;
                _logger.LogDebug("BackpressureController disposed");
            }
        }
    }
}
