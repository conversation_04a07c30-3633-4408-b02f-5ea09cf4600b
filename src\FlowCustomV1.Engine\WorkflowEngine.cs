using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using FlowCustomV1.Core.Exceptions;
using FlowCustomV1.Core.Events;
using FlowCustomV1.Engine.Configuration;
using FlowCustomV1.Engine.Concurrency;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;
using System.Threading.Channels;
using static FlowCustomV1.Core.Interfaces.IWorkflowEngine;

namespace FlowCustomV1.Engine;

/// <summary>
/// 工作流执行引擎
/// </summary>
public partial class WorkflowEngine : IWorkflowEngine, IDisposable
{
    private readonly ILogger<WorkflowEngine> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IPluginManager _pluginManager;
    private readonly INodeStatusEventPublisher? _eventPublisher;
    private readonly WorkflowEngineConfiguration _configuration;
    private readonly IWorkflowExecutionLogger _workflowLogger;
    private readonly IPerformanceLogger _performanceLogger;
    private readonly ConcurrentDictionary<Guid, WorkflowExecutionContext> _runningExecutions;
    private readonly Channel<WorkflowExecutionRequest> _executionQueue;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly AdaptiveSemaphore _concurrencyLimiter;
    private readonly BackpressureController _backpressureController;
    private Timer? _cleanupTimer;
    private Timer? _metricsTimer;
    private WorkflowEngineMetrics _currentMetrics;
    private volatile bool _isFullyInitialized = false;

    public event EventHandler<WorkflowExecutionStatusChangedEventArgs>? ExecutionStatusChanged;
    public event EventHandler<NodeExecutionStatusChangedEventArgs>? NodeExecutionStatusChanged;

    public WorkflowEngine(
        ILogger<WorkflowEngine> logger,
        IServiceScopeFactory serviceScopeFactory,
        IPluginManager pluginManager,
        ILoggerFactory loggerFactory,
        WorkflowEngineConfiguration? configuration = null,
        INodeStatusEventPublisher? eventPublisher = null,
        IWorkflowExecutionLogger? workflowLogger = null,
        IPerformanceLogger? performanceLogger = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
        _pluginManager = pluginManager ?? throw new ArgumentNullException(nameof(pluginManager));
        var factory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        _eventPublisher = eventPublisher;
        _configuration = configuration ?? WorkflowEngineConfiguration.GetDefault();

        // 使用提供的日志服务或创建默认实现，避免在构造函数中创建scope
        _workflowLogger = workflowLogger ?? new NullWorkflowExecutionLogger(_logger);
        _performanceLogger = performanceLogger ?? new NullPerformanceLogger(_logger);
        _configuration.Validate();

        _runningExecutions = new ConcurrentDictionary<Guid, WorkflowExecutionContext>();
        _cancellationTokenSource = new CancellationTokenSource();
        _currentMetrics = new WorkflowEngineMetrics();

        // 动态并发控制 - 使用自适应信号量
        var maxConcurrency = _configuration.MaxConcurrentExecutions;
        // 通过LoggerFactory创建AdaptiveSemaphore的专用logger
        var adaptiveSemaphoreLogger = factory.CreateLogger<AdaptiveSemaphore>();
        _concurrencyLimiter = new AdaptiveSemaphore(maxConcurrency, maxConcurrency, adaptiveSemaphoreLogger);

        // 初始化背压控制器
        var backpressureLogger = factory.CreateLogger<BackpressureController>();
        var backpressureSettings = _configuration.EnableAdaptiveResourceManagement
            ? BackpressureSettings.GetSensitive()
            : BackpressureSettings.GetDefault();
        _backpressureController = new BackpressureController(backpressureSettings, backpressureLogger);

        // 延迟初始化定时器，避免在构造函数中立即执行
        Task.Run(async () =>
        {
            await Task.Delay(TimeSpan.FromSeconds(10)); // 延迟10秒启动

            // 初始化清理定时器
            var cleanupInterval = TimeSpan.FromMinutes(_configuration.CleanupIntervalMinutes);
            _cleanupTimer = new Timer(CleanupExpiredExecutions, null, TimeSpan.Zero, cleanupInterval);

            // 初始化性能监控定时器
            if (_configuration.EnablePerformanceMonitoring)
            {
                _metricsTimer = new Timer(UpdateMetrics, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
            }

            _logger.LogInformation("工作流引擎定时器已启动");
        });

        // 创建高容量执行队列 - 支持更大的吞吐量
        var queueCapacity = _configuration.ExecutionQueueCapacity;
        var options = new BoundedChannelOptions(queueCapacity)
        {
            FullMode = BoundedChannelFullMode.DropOldest, // 队列满时丢弃最旧的请求
            SingleReader = false,
            SingleWriter = false,
            AllowSynchronousContinuations = false // 避免同步延续影响性能
        };
        _executionQueue = Channel.CreateBounded<WorkflowExecutionRequest>(options);

        _logger.LogInformation("工作流引擎初始化完成: 最大并发={MaxConcurrency}, 队列容量={QueueCapacity}, 配置={Configuration}",
            maxConcurrency, queueCapacity, _configuration.GetType().Name);

        // 延迟启动执行处理器，避免在构造函数中立即启动后台任务
        Task.Run(async () =>
        {
            await Task.Delay(TimeSpan.FromSeconds(5)); // 延迟5秒启动
            _ = ProcessExecutionQueueAsync();
            _isFullyInitialized = true;
            _logger.LogInformation("工作流引擎执行处理器已启动，完全初始化完成");
        });
    }

    /// <summary>
    /// 执行工作流
    /// </summary>
    public async Task<WorkflowExecution> ExecuteAsync(
        Guid workflowId,
        Dictionary<string, object>? inputData = null,
        string? triggeredBy = null,
        CancellationToken cancellationToken = default)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var workflowRepository = scope.ServiceProvider.GetRequiredService<IWorkflowRepository>();

        var workflow = await workflowRepository.GetByIdAsync(workflowId, cancellationToken);
        if (workflow == null)
        {
            throw new WorkflowException($"工作流不存在: {workflowId}", workflowId);
        }

        return await ExecuteAsync(workflow, inputData, triggeredBy, cancellationToken);
    }

    /// <summary>
    /// 执行工作流
    /// </summary>
    public async Task<WorkflowExecution> ExecuteAsync(
        WorkflowDefinition workflow,
        Dictionary<string, object>? inputData = null,
        string? triggeredBy = null,
        CancellationToken cancellationToken = default)
    {
        // 验证工作流
        var validationResult = await ValidateAsync(workflow, cancellationToken);
        if (!validationResult.IsValid)
        {
            throw new WorkflowValidationException(
                validationResult.Errors.Select(e => e.Message).ToList(),
                workflow.Id);
        }

        // 创建执行记录
        var execution = new WorkflowExecution
        {
            WorkflowDefinitionId = workflow.Id,
            // 不设置 WorkflowDefinition 导航属性，避免EF Core尝试插入重复的工作流定义
            Status = WorkflowExecutionStatus.Pending,
            InputData = inputData ?? new Dictionary<string, object>(),
            TriggeredBy = triggeredBy,
            CreatedAt = DateTime.UtcNow
        };

        using (var scope = _serviceScopeFactory.CreateScope())
        {
            var executionRepository = scope.ServiceProvider.GetRequiredService<IWorkflowExecutionRepository>();
            execution = await executionRepository.CreateAsync(execution, cancellationToken);
        }

        // 加入执行队列
        var request = new WorkflowExecutionRequest
        {
            Execution = execution,
            CancellationToken = cancellationToken
        };

        await _executionQueue.Writer.WriteAsync(request, cancellationToken);

        _logger.LogInformation("工作流已加入执行队列: {ExecutionId}", execution.Id);
        return execution;
    }

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    public async Task<bool> CancelAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        if (_runningExecutions.TryGetValue(executionId, out var context))
        {
            context.CancellationTokenSource.Cancel();
            _logger.LogInformation("工作流执行已取消: {ExecutionId}", executionId);
            return true;
        }

        // 如果不在运行中，更新数据库状态
        using var scope = _serviceScopeFactory.CreateScope();
        var executionRepository = scope.ServiceProvider.GetRequiredService<IWorkflowExecutionRepository>();

        var execution = await executionRepository.GetByIdAsync(executionId, cancellationToken);
        if (execution != null && execution.Status == WorkflowExecutionStatus.Pending)
        {
            execution.Status = WorkflowExecutionStatus.Cancelled;
            execution.CompletedAt = DateTime.UtcNow;
            await executionRepository.UpdateAsync(execution, cancellationToken);
            return true;
        }

        return false;
    }

    /// <summary>
    /// 暂停工作流执行
    /// </summary>
    public async Task<bool> PauseAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        if (_runningExecutions.TryGetValue(executionId, out var context))
        {
            context.IsPaused = true;
            var execution = context.Execution;
            execution.Status = WorkflowExecutionStatus.Paused;
            using var scope = _serviceScopeFactory.CreateScope();
            var executionRepository = scope.ServiceProvider.GetRequiredService<IWorkflowExecutionRepository>();
            await executionRepository.UpdateAsync(execution, cancellationToken);

            OnExecutionStatusChanged(new WorkflowExecutionStatusChangedEventArgs
            {
                ExecutionId = executionId,
                WorkflowId = execution.WorkflowDefinitionId,
                OldStatus = WorkflowExecutionStatus.Running,
                NewStatus = WorkflowExecutionStatus.Paused
            });

            _logger.LogInformation("工作流执行已暂停: {ExecutionId}", executionId);
            return true;
        }

        return false;
    }

    /// <summary>
    /// 恢复工作流执行
    /// </summary>
    public async Task<bool> ResumeAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        if (_runningExecutions.TryGetValue(executionId, out var context))
        {
            context.IsPaused = false;
            var execution = context.Execution;
            execution.Status = WorkflowExecutionStatus.Running;
            using var scope = _serviceScopeFactory.CreateScope();
            var executionRepository = scope.ServiceProvider.GetRequiredService<IWorkflowExecutionRepository>();
            await executionRepository.UpdateAsync(execution, cancellationToken);

            OnExecutionStatusChanged(new WorkflowExecutionStatusChangedEventArgs
            {
                ExecutionId = executionId,
                WorkflowId = execution.WorkflowDefinitionId,
                OldStatus = WorkflowExecutionStatus.Paused,
                NewStatus = WorkflowExecutionStatus.Running
            });

            _logger.LogInformation("工作流执行已恢复: {ExecutionId}", executionId);
            return true;
        }

        return false;
    }

    /// <summary>
    /// 获取工作流执行状态
    /// </summary>
    public async Task<WorkflowExecution?> GetExecutionStatusAsync(Guid executionId, CancellationToken cancellationToken = default)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var executionRepository = scope.ServiceProvider.GetRequiredService<IWorkflowExecutionRepository>();
        return await executionRepository.GetByIdAsync(executionId, cancellationToken);
    }

    /// <summary>
    /// 获取正在运行的工作流执行列表
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var executionRepository = scope.ServiceProvider.GetRequiredService<IWorkflowExecutionRepository>();
        return await executionRepository.GetRunningAsync(cancellationToken);
    }

    /// <summary>
    /// 验证工作流定义
    /// </summary>
    public async Task<WorkflowValidationResult> ValidateAsync(WorkflowDefinition workflow, CancellationToken cancellationToken = default)
    {
        var result = new WorkflowValidationResult { IsValid = true };

        try
        {
            // 基础验证
            if (string.IsNullOrWhiteSpace(workflow.Name))
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "WORKFLOW_NAME_REQUIRED",
                    Message = "工作流名称不能为空"
                });
            }

            // 暂时允许创建空的工作流，在设计器中添加节点
            // if (!workflow.Nodes.Any())
            // {
            //     result.Errors.Add(new ValidationError
            //     {
            //         Code = "NO_NODES",
            //         Message = "工作流必须包含至少一个节点"
            //     });
            // }

            // 验证节点
            await ValidateNodesAsync(workflow, result, cancellationToken);

            // 验证连接
            ValidateConnections(workflow, result);

            // 验证循环依赖
            ValidateCyclicDependencies(workflow, result);

            result.IsValid = !result.Errors.Any();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "工作流验证失败: {WorkflowId}", workflow.Id);
            result.Errors.Add(new ValidationError
            {
                Code = "VALIDATION_ERROR",
                Message = $"验证过程中发生错误: {ex.Message}"
            });
            result.IsValid = false;
        }

        return result;
    }

    /// <summary>
    /// 处理执行队列
    /// </summary>
    private async Task ProcessExecutionQueueAsync()
    {
        await foreach (var request in _executionQueue.Reader.ReadAllAsync(_cancellationTokenSource.Token))
        {
            try
            {
                // 检查队列长度并应用背压控制
                var queueLength = _executionQueue.Reader.CanCount ? _executionQueue.Reader.Count : 0;
                var queueCapacity = _configuration.ExecutionQueueCapacity;

                // 应用背压延迟（如果需要）
                await _backpressureController.ApplyBackpressureAsync(queueLength, queueCapacity, _cancellationTokenSource.Token);

                // 等待并发许可
                await _concurrencyLimiter.WaitAsync(_cancellationTokenSource.Token);

                // 使用Task.Run执行，但有并发控制
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await ExecuteWorkflowInternalAsync(request);
                    }
                    finally
                    {
                        // 释放并发许可
                        _concurrencyLimiter.Release();
                    }
                }, _cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理执行请求失败: {ExecutionId}", request.Execution.Id);
                // 如果获取许可失败，也要释放
                _concurrencyLimiter.Release();
            }
        }
    }

    /// <summary>
    /// 内部执行工作流
    /// </summary>
    private async Task ExecuteWorkflowInternalAsync(WorkflowExecutionRequest request)
    {
        var execution = request.Execution;
        var cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(request.CancellationToken);

        // 手动加载 WorkflowDefinition，因为在创建执行记录时没有设置导航属性
        if (execution.WorkflowDefinition == null)
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var workflowRepository = scope.ServiceProvider.GetRequiredService<IWorkflowRepository>();
            execution.WorkflowDefinition = await workflowRepository.GetByIdAsync(execution.WorkflowDefinitionId, cancellationTokenSource.Token);

            _logger.LogDebug("手动加载工作流定义: {WorkflowId}, 节点数量: {NodeCount}",
                execution.WorkflowDefinitionId,
                execution.WorkflowDefinition?.Nodes?.Count ?? -1);
        }

        var context = new WorkflowExecutionContext
        {
            Execution = execution,
            CancellationTokenSource = cancellationTokenSource,
            Variables = new Dictionary<string, object>(execution.InputData),
            NodeOutputs = new ConcurrentDictionary<string, Dictionary<string, object>>(),
            ServiceProvider = _serviceScopeFactory.CreateScope().ServiceProvider,
            StartedAt = DateTime.UtcNow
        };

        _runningExecutions.TryAdd(execution.Id, context);

        try
        {
            // 更新状态为运行中
            execution.Status = WorkflowExecutionStatus.Running;
            execution.StartedAt = DateTime.UtcNow;

            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var executionRepository = scope.ServiceProvider.GetRequiredService<IWorkflowExecutionRepository>();
                await executionRepository.UpdateAsync(execution, cancellationTokenSource.Token);
            }

            OnExecutionStatusChanged(new WorkflowExecutionStatusChangedEventArgs
            {
                ExecutionId = execution.Id,
                WorkflowId = execution.WorkflowDefinitionId,
                OldStatus = WorkflowExecutionStatus.Pending,
                NewStatus = WorkflowExecutionStatus.Running
            });

            // 执行工作流节点 - 使用插件系统
            await ExecuteWorkflowNodesAsync(context);

            // 更新状态为完成
            execution.Status = WorkflowExecutionStatus.Completed;
            execution.CompletedAt = DateTime.UtcNow;
            execution.OutputData = context.Variables;

            OnExecutionStatusChanged(new WorkflowExecutionStatusChangedEventArgs
            {
                ExecutionId = execution.Id,
                WorkflowId = execution.WorkflowDefinitionId,
                OldStatus = WorkflowExecutionStatus.Running,
                NewStatus = WorkflowExecutionStatus.Completed
            });
        }
        catch (OperationCanceledException)
        {
            execution.Status = WorkflowExecutionStatus.Cancelled;
            execution.CompletedAt = DateTime.UtcNow;
            _logger.LogInformation("工作流执行被取消: {ExecutionId}", execution.Id);
        }
        catch (Exception ex)
        {
            execution.Status = WorkflowExecutionStatus.Failed;
            execution.CompletedAt = DateTime.UtcNow;
            execution.ErrorMessage = ex.Message;
            execution.ErrorDetails = ex.ToString();

            _logger.LogError(ex, "工作流执行失败: {ExecutionId}", execution.Id);

            OnExecutionStatusChanged(new WorkflowExecutionStatusChangedEventArgs
            {
                ExecutionId = execution.Id,
                WorkflowId = execution.WorkflowDefinitionId,
                OldStatus = WorkflowExecutionStatus.Running,
                NewStatus = WorkflowExecutionStatus.Failed,
                ErrorMessage = ex.Message
            });
        }
        finally
        {
            // 使用独立的scope更新最终状态
            try
            {
                using var finalScope = _serviceScopeFactory.CreateScope();
                var executionRepository = finalScope.ServiceProvider.GetRequiredService<IWorkflowExecutionRepository>();
                await executionRepository.UpdateAsync(execution, CancellationToken.None);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新工作流执行最终状态失败: {ExecutionId}", execution.Id);
            }

            // 清理资源
            _runningExecutions.TryRemove(execution.Id, out _);
            cancellationTokenSource.Dispose();

            // 如果context中有ServiceProvider，也要释放
            if (context.ServiceProvider is IServiceScope scope)
            {
                scope.Dispose();
            }
        }
    }

    /// <summary>
    /// 清理过期的执行上下文
    /// </summary>
    private void CleanupExpiredExecutions(object? state)
    {
        try
        {
            var expiredExecutions = new List<Guid>();
            var cutoffTime = DateTime.UtcNow.AddHours(-_configuration.ExecutionContextExpirationHours);

            foreach (var kvp in _runningExecutions)
            {
                var context = kvp.Value;
                if (context.StartedAt.HasValue && context.StartedAt.Value < cutoffTime)
                {
                    expiredExecutions.Add(kvp.Key);
                }
            }

            foreach (var executionId in expiredExecutions)
            {
                if (_runningExecutions.TryRemove(executionId, out var context))
                {
                    _logger.LogWarning("清理过期的工作流执行: {ExecutionId}", executionId);

                    // 取消执行
                    context.CancellationTokenSource?.Cancel();
                    context.CancellationTokenSource?.Dispose();

                    // 释放ServiceProvider
                    if (context.ServiceProvider is IServiceScope scope)
                    {
                        scope.Dispose();
                    }
                }
            }

            if (expiredExecutions.Count > 0)
            {
                _logger.LogInformation("清理了 {Count} 个过期的工作流执行", expiredExecutions.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期执行时发生错误");
        }
    }

    private void OnExecutionStatusChanged(WorkflowExecutionStatusChangedEventArgs args)
    {
        ExecutionStatusChanged?.Invoke(this, args);
    }

    private void OnNodeExecutionStatusChanged(NodeExecutionStatusChangedEventArgs args)
    {
        NodeExecutionStatusChanged?.Invoke(this, args);
    }

    private async Task ValidateNodesAsync(WorkflowDefinition workflow, WorkflowValidationResult result, CancellationToken cancellationToken)
    {
        // 节点验证逻辑
        foreach (var node in workflow.Nodes)
        {
            if (string.IsNullOrWhiteSpace(node.Id))
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "NODE_ID_REQUIRED",
                    Message = "节点ID不能为空",
                    NodeId = node.Id
                });
            }

            if (string.IsNullOrWhiteSpace(node.Type))
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "NODE_TYPE_REQUIRED",
                    Message = "节点类型不能为空",
                    NodeId = node.Id
                });
            }

            // 简化版本：跳过插件验证
        }
    }

    private void ValidateConnections(WorkflowDefinition workflow, WorkflowValidationResult result)
    {
        // 连接验证逻辑
        var processedConnections = new HashSet<string>();

        foreach (var connection in workflow.Connections)
        {
            // 1. 检查节点是否存在
            var sourceNode = workflow.Nodes.FirstOrDefault(n => n.Id == connection.SourceNodeId);
            var targetNode = workflow.Nodes.FirstOrDefault(n => n.Id == connection.TargetNodeId);

            if (sourceNode == null)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "SOURCE_NODE_NOT_FOUND",
                    Message = $"源节点不存在: {connection.SourceNodeId}"
                });
                continue;
            }

            if (targetNode == null)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "TARGET_NODE_NOT_FOUND",
                    Message = $"目标节点不存在: {connection.TargetNodeId}"
                });
                continue;
            }

            // 2. 检查自连接
            if (connection.SourceNodeId == connection.TargetNodeId)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "SELF_CONNECTION_NOT_ALLOWED",
                    Message = $"不允许节点自连接: {connection.SourceNodeId}"
                });
                continue;
            }

            // 3. 检查重复连接
            var connectionKey = $"{connection.SourceNodeId}:{connection.SourceEndpointId}->{connection.TargetNodeId}:{connection.TargetEndpointId}";

            _logger.LogDebug("检查连接: {ConnectionKey}", connectionKey);
            _logger.LogDebug("已处理的连接: {ProcessedConnections}", string.Join(", ", processedConnections));

            if (processedConnections.Contains(connectionKey))
            {
                _logger.LogWarning("发现重复连接: {ConnectionKey}", connectionKey);
                result.Errors.Add(new ValidationError
                {
                    Code = "DUPLICATE_CONNECTION",
                    Message = $"重复连接: {connection.SourceNodeId}.{connection.SourceEndpointId} -> {connection.TargetNodeId}.{connection.TargetEndpointId}"
                });
                continue;
            }
            processedConnections.Add(connectionKey);

            // 4. 检查端点是否存在（支持按ID或Name匹配）
            var sourceEndpoint = sourceNode.OutputEndpoints?.FirstOrDefault(e =>
                e.Id == connection.SourceEndpointId || e.Name == connection.SourceEndpointId);
            var targetEndpoint = targetNode.InputEndpoints?.FirstOrDefault(e =>
                e.Id == connection.TargetEndpointId || e.Name == connection.TargetEndpointId);

            if (sourceEndpoint == null)
            {
                result.Warnings.Add(new ValidationWarning
                {
                    Code = "SOURCE_ENDPOINT_NOT_FOUND",
                    Message = $"源端点不存在: {connection.SourceNodeId}.{connection.SourceEndpointId}"
                });
            }

            if (targetEndpoint == null)
            {
                result.Warnings.Add(new ValidationWarning
                {
                    Code = "TARGET_ENDPOINT_NOT_FOUND",
                    Message = $"目标端点不存在: {connection.TargetNodeId}.{connection.TargetEndpointId}"
                });
            }

            // 5. 检查数据类型兼容性（如果端点都存在）
            if (sourceEndpoint != null && targetEndpoint != null)
            {
                if (!IsDataTypeCompatible(sourceEndpoint.Type, targetEndpoint.Type))
                {
                    result.Warnings.Add(new ValidationWarning
                    {
                        Code = "DATA_TYPE_INCOMPATIBLE",
                        Message = $"数据类型不兼容: {sourceEndpoint.Type} -> {targetEndpoint.Type}"
                    });
                }
            }
        }
    }

    /// <summary>
    /// 检查数据类型兼容性
    /// </summary>
    private bool IsDataTypeCompatible(string sourceType, string targetType)
    {
        // Any类型与所有类型兼容
        if (sourceType == "Any" || targetType == "Any")
            return true;

        // 相同类型兼容
        if (sourceType == targetType)
            return true;

        // 数字类型之间的兼容性
        var numericTypes = new[] { "Number", "Integer", "Float", "Double" };
        if (numericTypes.Contains(sourceType) && numericTypes.Contains(targetType))
            return true;

        // 文本类型兼容性
        var textTypes = new[] { "String", "Text" };
        if (textTypes.Contains(sourceType) && textTypes.Contains(targetType))
            return true;

        return false;
    }

    private void ValidateCyclicDependencies(WorkflowDefinition workflow, WorkflowValidationResult result)
    {
        // 循环依赖检测逻辑
        var visited = new HashSet<string>();
        var recursionStack = new HashSet<string>();

        foreach (var node in workflow.Nodes)
        {
            if (HasCycle(node.Id, workflow, visited, recursionStack))
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "CYCLIC_DEPENDENCY",
                    Message = "检测到循环依赖",
                    NodeId = node.Id
                });
                break;
            }
        }
    }

    private bool HasCycle(string nodeId, WorkflowDefinition workflow, HashSet<string> visited, HashSet<string> recursionStack)
    {
        if (recursionStack.Contains(nodeId))
            return true;

        if (visited.Contains(nodeId))
            return false;

        visited.Add(nodeId);
        recursionStack.Add(nodeId);

        var connections = workflow.Connections.Where(c => c.SourceNodeId == nodeId);
        foreach (var connection in connections)
        {
            if (HasCycle(connection.TargetNodeId, workflow, visited, recursionStack))
                return true;
        }

        recursionStack.Remove(nodeId);
        return false;
    }

    /// <summary>
    /// 执行工作流（使用工作流定义）
    /// </summary>
    public async Task<WorkflowExecution> ExecuteWorkflowAsync(
        WorkflowDefinition workflow,
        Dictionary<string, object>? inputData = null,
        string? triggeredBy = null,
        CancellationToken cancellationToken = default)
    {
        return await ExecuteAsync(workflow, inputData, triggeredBy, cancellationToken);
    }

    /// <summary>
    /// 执行工作流节点（使用插件系统）
    /// </summary>
    private async Task ExecuteWorkflowNodesAsync(WorkflowExecutionContext context)
    {
        var workflow = context.Execution.WorkflowDefinition;

        // 添加详细的调试日志
        _logger.LogInformation("调试信息 - 工作流定义状态: ExecutionId={ExecutionId}, WorkflowDefinition={IsNull}, NodesCount={NodesCount}",
            context.Execution.Id,
            workflow == null ? "null" : "not null",
            workflow?.Nodes?.Count ?? -1);

        if (workflow != null && workflow.Nodes != null)
        {
            _logger.LogInformation("节点详细信息: {NodeDetails}",
                string.Join(", ", workflow.Nodes.Select(n => $"{n.Id}({n.Type})")));
        }

        if (workflow == null || !workflow.Nodes.Any())
        {
            _logger.LogInformation("工作流没有节点，执行完成: {ExecutionId}", context.Execution.Id);
            return;
        }

        _logger.LogInformation("开始执行工作流节点: {ExecutionId}, 节点数量: {NodeCount}",
            context.Execution.Id, workflow.Nodes.Count);

        // 构建执行图
        var executionGraph = BuildExecutionGraph(workflow);

        // 找到起始节点（没有输入连接的节点）
        var startNodes = FindStartNodes(workflow);

        if (!startNodes.Any())
        {
            _logger.LogWarning("工作流中没有找到起始节点: {ExecutionId}", context.Execution.Id);
            return;
        }

        // 高效并行执行起始节点 - 使用并发控制
        var semaphore = new SemaphoreSlim(Math.Min(startNodes.Count, Environment.ProcessorCount * 2));
        var tasks = startNodes.Select(async node =>
        {
            await semaphore.WaitAsync(context.CancellationTokenSource.Token);
            try
            {
                await ExecuteNodeAsync(node, context, executionGraph);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
        semaphore.Dispose();

        _logger.LogInformation("工作流节点执行完成: {ExecutionId}", context.Execution.Id);
    }

    /// <summary>
    /// 构建执行图
    /// </summary>
    private ExecutionGraph BuildExecutionGraph(WorkflowDefinition workflow)
    {
        return new ExecutionGraph(workflow);
    }

    /// <summary>
    /// 找到起始节点
    /// </summary>
    private List<WorkflowNode> FindStartNodes(WorkflowDefinition workflow)
    {
        return workflow.Nodes.Where(node =>
        {
            // 检查是否有输入连接
            var hasInputConnection = workflow.Connections.Any(conn => conn.TargetNodeId == node.Id);
            return !hasInputConnection || node.Type == "Start";
        }).ToList();
    }

    /// <summary>
    /// 执行单个节点
    /// </summary>
    private async Task ExecuteNodeAsync(WorkflowNode node, WorkflowExecutionContext context, ExecutionGraph executionGraph)
    {
        var cancellationToken = context.CancellationTokenSource.Token;

        try
        {
            _logger.LogDebug("开始执行节点: {NodeId} ({NodeType})", node.Id, node.Type);

            // 创建节点执行记录 - 使用EF Core级联更新设计
            var nodeExecution = new NodeExecution
            {
                WorkflowExecutionId = context.Execution.Id,
                NodeId = node.Id,
                NodeName = node.Name,
                NodeType = node.Type,
                Status = NodeExecutionStatus.Running,
                StartedAt = DateTime.UtcNow,
                InputData = new Dictionary<string, object>(),
                OutputData = new Dictionary<string, object>()
            };

            // 添加到WorkflowExecution的NodeExecutions集合中，利用EF Core级联更新
            context.Execution.NodeExecutions.Add(nodeExecution);
            _logger.LogDebug("节点执行记录已添加到执行上下文: {NodeId}", node.Id);

            // 发布节点开始执行事件
            PublishNodeStatusEvent(context.Execution.Id.ToString(), node, "running", nodeExecution.StartedAt, null, null, null);

            // 准备输入数据
            var inputData = await PrepareNodeInputDataAsync(node, context, executionGraph);

            // 创建节点执行器
            var executor = await _pluginManager.CreateNodeExecutorAsync(node.PluginName, node.Type, cancellationToken);
            if (executor == null)
            {
                throw new InvalidOperationException($"无法创建节点执行器: {node.PluginName}.{node.Type}");
            }

            // 创建执行上下文
            var nodeContext = new NodeExecutionContext
            {
                NodeExecutionId = nodeExecution.Id,
                WorkflowExecutionId = context.Execution.Id,
                Node = node,
                InputData = inputData,
                Variables = context.Variables,
                Context = new Dictionary<string, object>(),
                Logger = new SimpleExecutionLogger(context.ServiceProvider.GetRequiredService<ILogger<WorkflowEngine>>()),
                ServiceProvider = context.ServiceProvider,
                CancellationToken = cancellationToken
            };

            // 执行节点
            var result = await executor.ExecuteAsync(nodeContext, cancellationToken);

            // 更新节点执行记录 - 通过EF Core级联更新自动处理
            nodeExecution.Status = result.IsSuccess ? NodeExecutionStatus.Completed : NodeExecutionStatus.Failed;
            nodeExecution.CompletedAt = DateTime.UtcNow;
            nodeExecution.OutputData = result.OutputData;

            if (!result.IsSuccess)
            {
                nodeExecution.ErrorMessage = result.ErrorMessage;
                nodeExecution.ErrorDetails = result.ErrorDetails;
            }

            // 发布节点执行完成事件
            PublishNodeStatusEvent(context.Execution.Id.ToString(), node,
                result.IsSuccess ? "completed" : "failed",
                nodeExecution.StartedAt, nodeExecution.CompletedAt,
                result.ErrorMessage, result.OutputData);

            _logger.LogDebug("节点执行记录已更新，将通过EF Core级联更新保存: {NodeId}", node.Id);

            // 保存节点输出
            if (result.IsSuccess)
            {
                context.NodeOutputs.TryAdd(node.Id, result.OutputData);
                _logger.LogDebug("节点执行成功: {NodeId}", node.Id);

                // 执行后续节点
                await ExecuteNextNodesAsync(node, context, executionGraph);
            }
            else
            {
                _logger.LogError("节点执行失败: {NodeId} - {ErrorMessage}", node.Id, result.ErrorMessage);

                // 根据错误处理策略决定是否继续执行
                var errorHandling = context.Execution.WorkflowDefinition?.Configuration?.ErrorHandling ?? ErrorHandlingStrategy.StopOnError;
                if (errorHandling == ErrorHandlingStrategy.StopOnError)
                {
                    throw new NodeExecutionException(
                        result.ErrorMessage ?? "节点执行失败",
                        result.Exception ?? new Exception("节点执行失败"),
                        node.Id,
                        node.Type);
                }
                else if (errorHandling == ErrorHandlingStrategy.IgnoreErrors)
                {
                    // 忽略错误，继续执行后续节点
                    await ExecuteNextNodesAsync(node, context, executionGraph);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("节点执行被取消: {NodeId}", node.Id);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "节点执行异常: {NodeId}", node.Id);
            throw;
        }
    }

    /// <summary>
    /// 准备节点输入数据
    /// </summary>
    private async Task<Dictionary<string, object>> PrepareNodeInputDataAsync(
        WorkflowNode node,
        WorkflowExecutionContext context,
        ExecutionGraph executionGraph)
    {
        var inputData = new Dictionary<string, object>();

        // 从前置节点收集输出数据
        var previousNodes = executionGraph.GetPreviousNodes(node.Id);
        foreach (var previousNode in previousNodes)
        {
            if (context.NodeOutputs.TryGetValue(previousNode.Id, out var sourceOutput))
            {
                // 将前置节点的输出作为当前节点的输入
                foreach (var kvp in sourceOutput)
                {
                    inputData[kvp.Key] = kvp.Value;
                }
            }
        }

        // 添加工作流变量
        foreach (var variable in context.Variables)
        {
            inputData[$"workflow.{variable.Key}"] = variable.Value;
        }

        return inputData;
    }

    /// <summary>
    /// 执行后续节点 - 优化版本
    /// </summary>
    private async Task ExecuteNextNodesAsync(WorkflowNode node, WorkflowExecutionContext context, ExecutionGraph executionGraph)
    {
        var nextNodes = executionGraph.GetNextNodes(node.Id);

        if (!nextNodes.Any()) return;

        // 检查节点依赖是否满足
        var readyNodes = nextNodes.Where(nextNode =>
            executionGraph.AreAllDependenciesSatisfied(nextNode.Id, context.NodeOutputs.Keys)).ToList();

        if (!readyNodes.Any()) return;

        // 智能并发控制 - 根据节点复杂度调整并发数
        var maxConcurrency = CalculateNodeConcurrency(readyNodes);
        var semaphore = new SemaphoreSlim(maxConcurrency);

        var tasks = readyNodes.Select(async nextNode =>
        {
            await semaphore.WaitAsync(context.CancellationTokenSource.Token);
            try
            {
                await ExecuteNodeAsync(nextNode, context, executionGraph);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
        semaphore.Dispose();
    }

    /// <summary>
    /// 计算节点并发数
    /// </summary>
    private int CalculateNodeConcurrency(List<WorkflowNode> nodes)
    {
        // 基于节点类型和复杂度计算最优并发数
        var cpuIntensiveNodes = nodes.Count(n => IsCpuIntensive(n.Type));
        var ioIntensiveNodes = nodes.Count(n => IsIoIntensive(n.Type));
        var simpleNodes = nodes.Count - cpuIntensiveNodes - ioIntensiveNodes;

        // CPU密集型节点：限制为CPU核心数
        // IO密集型节点：可以更高并发
        // 简单节点：中等并发
        var optimalConcurrency = Math.Min(nodes.Count,
            Math.Max(1, Environment.ProcessorCount + ioIntensiveNodes + simpleNodes / 2));

        return Math.Min(optimalConcurrency, 50); // 最大50个并发节点
    }

    /// <summary>
    /// 判断是否为CPU密集型节点
    /// </summary>
    private bool IsCpuIntensive(string nodeType)
    {
        return nodeType.Contains("Script") || nodeType.Contains("Transform") ||
               nodeType.Contains("Calculate") || nodeType.Contains("Process");
    }

    /// <summary>
    /// 判断是否为IO密集型节点
    /// </summary>
    private bool IsIoIntensive(string nodeType)
    {
        return nodeType.Contains("Http") || nodeType.Contains("Database") ||
               nodeType.Contains("File") || nodeType.Contains("Api");
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetExecutionHistoryAsync(Guid workflowId, CancellationToken cancellationToken = default)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var executionRepository = scope.ServiceProvider.GetRequiredService<IWorkflowExecutionRepository>();
        var (items, _) = await executionRepository.GetAllAsync(workflowId, null, 0, 1000, cancellationToken);
        return items;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 更新性能指标
    /// </summary>
    private void UpdateMetrics(object? state)
    {
        try
        {
            _currentMetrics.RunningWorkflowCount = _runningExecutions.Count;
            _currentMetrics.QueuedWorkflowCount = _executionQueue.Reader.CanCount ?
                _executionQueue.Reader.Count : 0;
            _currentMetrics.MemoryUsageMB = GC.GetTotalMemory(false) / (1024 * 1024);
            _currentMetrics.LastUpdated = DateTime.UtcNow;

            // 记录性能指标到日志
            if (_currentMetrics.RunningWorkflowCount > 0 || _currentMetrics.QueuedWorkflowCount > 0)
            {
                _logger.LogDebug("性能指标: 运行中={Running}, 队列中={Queued}, 内存={Memory}MB",
                    _currentMetrics.RunningWorkflowCount,
                    _currentMetrics.QueuedWorkflowCount,
                    _currentMetrics.MemoryUsageMB);
            }

            // 检查资源阈值
            if (_configuration.EnableAdaptiveResourceManagement)
            {
                CheckResourceThresholds();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "更新性能指标时发生错误");
        }
    }

    /// <summary>
    /// 检查资源阈值
    /// </summary>
    private void CheckResourceThresholds()
    {
        // 检查内存使用
        if (_currentMetrics.MemoryUsageMB > _configuration.MemoryThresholdMB)
        {
            _logger.LogWarning("内存使用超过阈值: {Current}MB > {Threshold}MB",
                _currentMetrics.MemoryUsageMB, _configuration.MemoryThresholdMB);

            // 触发垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }

        // 检查运行中的工作流数量
        var runningCount = _currentMetrics.RunningWorkflowCount;
        var maxConcurrency = _configuration.MaxConcurrentExecutions;

        if (runningCount > maxConcurrency * 0.9) // 90%阈值
        {
            _logger.LogWarning("工作流并发数接近上限: {Current}/{Max}", runningCount, maxConcurrency);
        }
    }

    /// <summary>
    /// 获取当前性能指标
    /// </summary>
    public WorkflowEngineMetrics GetCurrentMetrics()
    {
        return _currentMetrics;
    }

    /// <summary>
    /// 获取引擎配置
    /// </summary>
    public WorkflowEngineConfiguration GetConfiguration()
    {
        return _configuration;
    }

    /// <summary>
    /// 动态调整并发限制
    /// </summary>
    public async Task AdjustConcurrencyAsync(int newLimit)
    {
        if (newLimit <= 0 || newLimit > 10000)
        {
            throw new ArgumentException("并发限制必须在1-10000之间", nameof(newLimit));
        }

        var currentStatus = _concurrencyLimiter.GetStatus();
        var currentLimit = currentStatus.MaxCount;

        if (newLimit != currentLimit)
        {
            // 直接调整信号量容量 - AdaptiveSemaphore会处理所有复杂逻辑
            _concurrencyLimiter.AdjustMaxCount(newLimit);
        }

        _logger.LogInformation("并发限制已调整: {OldLimit} -> {NewLimit}", currentLimit, newLimit);
    }

    /// <summary>
    /// 获取背压控制器状态
    /// </summary>
    /// <returns>背压控制器状态</returns>
    public BackpressureControllerStatus GetBackpressureStatus()
    {
        return _backpressureController.GetStatus();
    }

    /// <summary>
    /// 手动检查背压状态
    /// </summary>
    public void CheckBackpressureState()
    {
        var queueLength = _executionQueue.Reader.CanCount ? _executionQueue.Reader.Count : 0;
        var queueCapacity = _configuration.ExecutionQueueCapacity;
        _backpressureController.CheckState(queueLength, queueCapacity);
    }

    /// <summary>
    /// 发布节点状态事件
    /// </summary>
    private void PublishNodeStatusEvent(string workflowExecutionId, WorkflowNode node, string status,
        DateTime? startTime, DateTime? endTime, string? errorMessage, object? outputData)
    {
        if (_eventPublisher != null)
        {
            try
            {
                _logger.LogInformation("发送节点{Status}状态: {NodeId}", status == "running" ? "开始执行" : "执行完成", node.Id);

                var eventArgs = new NodeStatusChangedEventArgs
                {
                    WorkflowExecutionId = workflowExecutionId,
                    NodeId = node.Id,
                    NodeName = node.Name,
                    NodeType = node.Type,
                    Status = status,
                    StartTime = startTime,
                    EndTime = endTime,
                    ErrorMessage = errorMessage,
                    OutputData = outputData,
                    AdditionalData = new
                    {
                        nodeType = node.Type,
                        nodeName = node.Name,
                        executionTimeMs = endTime.HasValue && startTime.HasValue
                            ? (double?)(endTime.Value - startTime.Value).TotalMilliseconds
                            : null
                    }
                };

                _eventPublisher.PublishNodeStatusChanged(eventArgs);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "发送节点状态事件失败: {NodeId} - {Status}", node.Id, status);
            }
        }
        else
        {
            _logger.LogDebug("事件发布器不可用，跳过节点状态事件发布: {NodeId} - {Status}", node.Id, status);
        }
    }

    /// <summary>
    /// 获取当前活跃的执行数量
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃执行数量</returns>
    public Task<int> GetActiveExecutionCountAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(_runningExecutions.Count);
    }

    /// <summary>
    /// 获取当前活跃的执行数量（同步版本，供内部使用）
    /// </summary>
    /// <returns>活跃执行数量</returns>
    public int GetActiveExecutionCount()
    {
        return _runningExecutions.Count;
    }

    private bool _disposed = false;

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 停止队列处理
                _cancellationTokenSource?.Cancel();

                // 清理所有运行中的执行
                foreach (var kvp in _runningExecutions)
                {
                    var context = kvp.Value;
                    context.CancellationTokenSource?.Cancel();
                    context.CancellationTokenSource?.Dispose();

                    if (context.ServiceProvider is IServiceScope scope)
                    {
                        scope.Dispose();
                    }
                }
                _runningExecutions.Clear();

                // 释放资源
                _concurrencyLimiter?.Dispose();
                _backpressureController?.Dispose();
                _cleanupTimer?.Dispose();
                _metricsTimer?.Dispose();
                _cancellationTokenSource?.Dispose();
            }

            _disposed = true;
        }
    }
}

/// <summary>
/// 工作流执行请求
/// </summary>
internal class WorkflowExecutionRequest
{
    public WorkflowExecution Execution { get; set; } = null!;
    public CancellationToken CancellationToken { get; set; }
}

/// <summary>
/// 工作流执行上下文
/// </summary>
public class WorkflowExecutionContext
{
    public WorkflowExecution Execution { get; set; } = null!;
    public CancellationTokenSource CancellationTokenSource { get; set; } = null!;
    public Dictionary<string, object> Variables { get; set; } = new();
    public ConcurrentDictionary<string, Dictionary<string, object>> NodeOutputs { get; set; } = new();
    public bool IsPaused { get; set; }
    public IServiceProvider ServiceProvider { get; set; } = null!;
    public DateTime? StartedAt { get; set; }
}

/// <summary>
/// 简单执行日志记录器
/// </summary>
internal class SimpleExecutionLogger : IExecutionLogger
{
    private readonly ILogger _logger;

    public SimpleExecutionLogger(ILogger logger)
    {
        _logger = logger;
    }

    public void LogTrace(string message, params object[] args)
    {
        _logger.LogTrace(message, args);
    }

    public void LogDebug(string message, params object[] args)
    {
        _logger.LogDebug(message, args);
    }

    public void LogInformation(string message, params object[] args)
    {
        _logger.LogInformation(message, args);
    }

    public void LogWarning(string message, params object[] args)
    {
        _logger.LogWarning(message, args);
    }

    public void LogError(string message, Exception? exception = null, params object[] args)
    {
        if (exception != null)
        {
            _logger.LogError(exception, message, args);
        }
        else
        {
            _logger.LogError(message, args);
        }
    }

    public void LogCritical(string message, Exception? exception = null, params object[] args)
    {
        if (exception != null)
        {
            _logger.LogCritical(exception, message, args);
        }
        else
        {
            _logger.LogCritical(message, args);
        }
    }
}

/// <summary>
/// 空的工作流执行日志实现（用于向后兼容）
/// </summary>
internal class NullWorkflowExecutionLogger : IWorkflowExecutionLogger
{
    private readonly ILogger _logger;

    public NullWorkflowExecutionLogger(ILogger logger)
    {
        _logger = logger;
    }

    public void LogWorkflowStart(Guid workflowExecutionId, string workflowName, object? metadata = null)
    {
        _logger.LogInformation("工作流开始执行: {WorkflowName} (ExecutionId: {ExecutionId})", workflowName, workflowExecutionId);
    }

    public void LogWorkflowComplete(Guid workflowExecutionId, TimeSpan duration, object? result = null)
    {
        _logger.LogInformation("工作流执行完成，耗时: {Duration}ms (ExecutionId: {ExecutionId})", duration.TotalMilliseconds, workflowExecutionId);
    }

    public void LogWorkflowError(Guid workflowExecutionId, Exception exception, object? context = null)
    {
        _logger.LogError(exception, "工作流执行失败 (ExecutionId: {ExecutionId})", workflowExecutionId);
    }

    public void LogNodeStart(Guid workflowExecutionId, Guid nodeId, string nodeType, object? parameters = null)
    {
        _logger.LogDebug("节点开始执行: {NodeType} (ExecutionId: {ExecutionId}, NodeId: {NodeId})", nodeType, workflowExecutionId, nodeId);
    }

    public void LogNodeComplete(Guid workflowExecutionId, Guid nodeId, TimeSpan duration, object? result = null)
    {
        _logger.LogDebug("节点执行完成，耗时: {Duration}ms (ExecutionId: {ExecutionId}, NodeId: {NodeId})", duration.TotalMilliseconds, workflowExecutionId, nodeId);
    }

    public void LogNodeError(Guid workflowExecutionId, Guid nodeId, Exception exception, object? context = null)
    {
        _logger.LogError(exception, "节点执行失败 (ExecutionId: {ExecutionId}, NodeId: {NodeId})", workflowExecutionId, nodeId);
    }

    public void LogNodeStatusChange(Guid workflowExecutionId, Guid nodeId, string fromStatus, string toStatus)
    {
        _logger.LogDebug("节点状态变更: {FromStatus} -> {ToStatus} (ExecutionId: {ExecutionId}, NodeId: {NodeId})", fromStatus, toStatus, workflowExecutionId, nodeId);
    }
}

/// <summary>
/// 空的性能日志实现（用于向后兼容）
/// </summary>
internal class NullPerformanceLogger : IPerformanceLogger
{
    private readonly ILogger _logger;

    public NullPerformanceLogger(ILogger logger)
    {
        _logger = logger;
    }

    public void LogApiPerformance(string endpoint, string method, TimeSpan duration, int statusCode)
    {
        var level = duration.TotalMilliseconds > 1000 ? Microsoft.Extensions.Logging.LogLevel.Warning : Microsoft.Extensions.Logging.LogLevel.Information;
        _logger.Log(level, "API性能: {Method} {Endpoint} - {Duration}ms - {StatusCode}", method, endpoint, duration.TotalMilliseconds, statusCode);
    }

    public void LogDatabasePerformance(string operation, TimeSpan duration, int recordCount)
    {
        var level = duration.TotalMilliseconds > 500 ? Microsoft.Extensions.Logging.LogLevel.Warning : Microsoft.Extensions.Logging.LogLevel.Information;
        _logger.Log(level, "数据库性能: {Operation} - {Duration}ms - {RecordCount}条记录", operation, duration.TotalMilliseconds, recordCount);
    }

    public void LogMemoryUsage(long workingSet, long privateMemory, long gcMemory)
    {
        _logger.LogInformation("内存使用: WorkingSet={WorkingSet}MB, Private={PrivateMemory}MB, GC={GcMemory}MB",
            workingSet / 1024 / 1024, privateMemory / 1024 / 1024, gcMemory / 1024 / 1024);
    }

    public void LogConcurrencyMetrics(int activeWorkflows, int queuedWorkflows, int availableThreads)
    {
        _logger.LogInformation("并发指标: Active={Active}, Queued={Queued}, Threads={Threads}", activeWorkflows, queuedWorkflows, availableThreads);
    }

    public void LogThroughputMetrics(int requestsPerSecond, int workflowsPerSecond)
    {
        _logger.LogInformation("吞吐量指标: Requests={RequestsPerSecond}/s, Workflows={WorkflowsPerSecond}/s", requestsPerSecond, workflowsPerSecond);
    }

    public void LogResourceUtilization(double cpuUsage, double memoryUsage, double diskUsage)
    {
        var level = (cpuUsage > 80 || memoryUsage > 80 || diskUsage > 80) ? Microsoft.Extensions.Logging.LogLevel.Warning : Microsoft.Extensions.Logging.LogLevel.Information;
        _logger.Log(level, "资源利用率: CPU={CpuUsage:F1}%, Memory={MemoryUsage:F1}%, Disk={DiskUsage:F1}%", cpuUsage, memoryUsage, diskUsage);
    }
}
