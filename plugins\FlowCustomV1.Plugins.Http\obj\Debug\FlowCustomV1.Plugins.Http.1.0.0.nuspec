﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>FlowCustomV1.Plugins.Http</id>
    <version>1.0.0</version>
    <authors>FlowCustomV1</authors>
    <description>HTTP operations plugin for FlowCustomV1 workflow engine</description>
    <repository type="git" commit="adbcad2cdf6fd4950c8f8f869e3fbc15cef7ad6b" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="FlowCustomV1.Core" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="FlowCustomV1.SDK" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Http" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="8.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\Documents\augment-projects\FlowCustomV1\plugins\FlowCustomV1.Plugins.Http\bin\Debug\net8.0\FlowCustomV1.Plugins.Http.dll" target="lib\net8.0\FlowCustomV1.Plugins.Http.dll" />
  </files>
</package>