# Test API connection and database functionality

Write-Host "Testing FlowCustomV1 API connection..." -ForegroundColor Green

# Test 1: Get workflows (should return empty array)
Write-Host "`n1. Testing GET /api/workflows..." -ForegroundColor Yellow
try {
    $workflows = Invoke-RestMethod -Uri "http://localhost:5279/api/workflows" -Method Get
    Write-Host "✅ GET /api/workflows successful" -ForegroundColor Green
    Write-Host "Response: $($workflows | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ GET /api/workflows failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Create a workflow
Write-Host "`n2. Testing POST /api/workflows..." -ForegroundColor Yellow
$testWorkflow = @{
    Name = "Test Workflow $(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Description = "Test workflow created by API test"
    Nodes = @()
    Connections = @()
    Variables = @{}
    Configuration = @{
        TimeoutSeconds = 300
        MaxRetryCount = 3
        EnableParallelExecution = $true
        ErrorHandling = "StopOnError"
        LogLevel = "Information"
    }
} | ConvertTo-Json -Depth 3

try {
    $newWorkflow = Invoke-RestMethod -Uri "http://localhost:5279/api/workflows" -Method Post -Body $testWorkflow -ContentType "application/json"
    Write-Host "✅ POST /api/workflows successful" -ForegroundColor Green
    Write-Host "Created workflow ID: $($newWorkflow.id)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ POST /api/workflows failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
    exit 1
}

# Test 3: Get workflows again (should now have one workflow)
Write-Host "`n3. Testing GET /api/workflows again..." -ForegroundColor Yellow
try {
    $workflows = Invoke-RestMethod -Uri "http://localhost:5279/api/workflows" -Method Get
    Write-Host "✅ GET /api/workflows successful" -ForegroundColor Green
    Write-Host "Found $($workflows.Count) workflow(s)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ GET /api/workflows failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 All API tests passed! Database connection is working correctly." -ForegroundColor Green
