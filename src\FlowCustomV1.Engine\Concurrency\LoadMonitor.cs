using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Engine.Services;

namespace FlowCustomV1.Engine.Concurrency
{
    /// <summary>
    /// 增强的负载监控器 - 提供全面的系统负载监控、历史数据分析和预测功能
    /// Task 1.5: 负载监控和反馈机制增强
    /// </summary>
    public class LoadMonitor : IDisposable
    {
        private readonly ILogger<LoadMonitor> _logger;
        private readonly SystemMetricsCollector _systemMetricsCollector;
        private readonly IWorkflowEngine? _workflowEngine;
        private readonly BackpressureController? _backpressureController;
        private readonly Timer _monitoringTimer;
        private readonly Timer _analysisTimer;
        
        // 历史数据存储 - 使用循环缓冲区存储最近的指标数据
        private readonly CircularBuffer<LoadMetrics> _loadHistory;
        private readonly CircularBuffer<LoadMetrics> _hourlyHistory;
        private readonly CircularBuffer<LoadMetrics> _dailyHistory;
        
        // 性能计数器
        private PerformanceCounter? _diskReadCounter;
        private PerformanceCounter? _diskWriteCounter;
        private PerformanceCounter? _networkSentCounter;
        private PerformanceCounter? _networkReceivedCounter;
        
        // 预测和分析
        private LoadPrediction? _currentPrediction;
        private LoadRecommendation? _currentRecommendation;
        private readonly object _analysisLock = new object();
        
        // 告警机制
        private readonly ConcurrentQueue<LoadAlert> _activeAlerts;
        private DateTime _lastAlertTime = DateTime.MinValue;
        private readonly TimeSpan _alertCooldown = TimeSpan.FromMinutes(5);
        
        // 配置
        private readonly LoadMonitorSettings _settings;
        private volatile bool _disposed = false;

        /// <summary>
        /// 当前负载指标
        /// </summary>
        public LoadMetrics CurrentMetrics { get; private set; }

        /// <summary>
        /// 当前负载预测
        /// </summary>
        public LoadPrediction? CurrentPrediction => _currentPrediction;

        /// <summary>
        /// 当前优化建议
        /// </summary>
        public LoadRecommendation? CurrentRecommendation => _currentRecommendation;

        /// <summary>
        /// 活跃告警列表
        /// </summary>
        public IEnumerable<LoadAlert> ActiveAlerts => _activeAlerts.ToArray();

        /// <summary>
        /// 初始化增强负载监控器
        /// </summary>
        /// <param name="systemMetricsCollector">系统指标收集器</param>
        /// <param name="settings">监控设置</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="workflowEngine">工作流引擎（可选）</param>
        /// <param name="backpressureController">背压控制器（可选）</param>
        public LoadMonitor(
            SystemMetricsCollector systemMetricsCollector,
            LoadMonitorSettings settings,
            ILogger<LoadMonitor> logger,
            IWorkflowEngine? workflowEngine = null,
            BackpressureController? backpressureController = null)
        {
            _systemMetricsCollector = systemMetricsCollector ?? throw new ArgumentNullException(nameof(systemMetricsCollector));
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _workflowEngine = workflowEngine; // 可选依赖
            _backpressureController = backpressureController; // 可选依赖

            // 初始化历史数据存储
            _loadHistory = new CircularBuffer<LoadMetrics>(_settings.HistoryCapacity);
            _hourlyHistory = new CircularBuffer<LoadMetrics>(_settings.HourlyHistoryCapacity);
            _dailyHistory = new CircularBuffer<LoadMetrics>(_settings.DailyHistoryCapacity);
            
            // 初始化告警队列
            _activeAlerts = new ConcurrentQueue<LoadAlert>();

            // 初始化性能计数器
            InitializePerformanceCounters();

            // 初始化当前指标
            CurrentMetrics = CreateInitialMetrics();

            // 启动监控定时器
            _monitoringTimer = new Timer(
                CollectMetrics!,
                null,
                TimeSpan.Zero,
                TimeSpan.FromSeconds(_settings.CollectionIntervalSeconds));

            // 启动分析定时器
            _analysisTimer = new Timer(
                PerformAnalysis!,
                null,
                TimeSpan.FromSeconds(_settings.AnalysisIntervalSeconds),
                TimeSpan.FromSeconds(_settings.AnalysisIntervalSeconds));

            _logger.LogInformation("LoadMonitor initialized with settings: {Settings}", _settings);
        }

        /// <summary>
        /// 初始化性能计数器
        /// </summary>
        private void InitializePerformanceCounters()
        {
            try
            {
                // 只在Windows平台上初始化PerformanceCounter
                if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                {
                    // 磁盘I/O计数器
                    _diskReadCounter = new PerformanceCounter("PhysicalDisk", "Disk Read Bytes/sec", "_Total");
                    _diskWriteCounter = new PerformanceCounter("PhysicalDisk", "Disk Write Bytes/sec", "_Total");

                    // 网络计数器
                    _networkSentCounter = new PerformanceCounter("Network Interface", "Bytes Sent/sec", "*");
                    _networkReceivedCounter = new PerformanceCounter("Network Interface", "Bytes Received/sec", "*");

                    // 预热计数器
                    _diskReadCounter?.NextValue();
                    _diskWriteCounter?.NextValue();
                    _networkSentCounter?.NextValue();
                    _networkReceivedCounter?.NextValue();

                    _logger.LogDebug("Windows性能计数器初始化成功");
                }
                else
                {
                    _logger.LogDebug("非Windows平台，跳过PerformanceCounter初始化，将使用替代方法");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize some performance counters, will use fallback methods");
            }
        }

        /// <summary>
        /// 创建初始指标
        /// </summary>
        private LoadMetrics CreateInitialMetrics()
        {
            var systemMetrics = _systemMetricsCollector.GetCurrentMetrics();
            return new LoadMetrics
            {
                Timestamp = DateTime.UtcNow,
                CpuUsage = systemMetrics.CpuUsage,
                MemoryUsage = systemMetrics.MemoryUsage,
                QueueLength = systemMetrics.QueueLength,
                AverageResponseTimeMs = systemMetrics.AverageResponseTimeMs,
                ErrorRate = systemMetrics.ErrorRate,
                DiskIOUsage = 0.0,
                NetworkIOUsage = 0.0,
                ActiveWorkflows = 0,
                BackpressureState = BackpressureState.Normal,
                LoadLevel = LoadLevel.Low
            };
        }

        /// <summary>
        /// 收集系统指标
        /// </summary>
        private void CollectMetrics(object state)
        {
            if (_disposed) return;

            try
            {
                var systemMetrics = _systemMetricsCollector.GetCurrentMetrics();
                var diskIO = GetDiskIOUsage();
                var networkIO = GetNetworkIOUsage();
                
                var newMetrics = new LoadMetrics
                {
                    Timestamp = DateTime.UtcNow,
                    CpuUsage = systemMetrics.CpuUsage,
                    MemoryUsage = systemMetrics.MemoryUsage,
                    QueueLength = systemMetrics.QueueLength,
                    AverageResponseTimeMs = systemMetrics.AverageResponseTimeMs,
                    ErrorRate = systemMetrics.ErrorRate,
                    DiskIOUsage = diskIO,
                    NetworkIOUsage = networkIO,
                    ActiveWorkflows = GetActiveWorkflowCount(),
                    BackpressureState = GetCurrentBackpressureState(),
                    LoadLevel = CalculateLoadLevel(systemMetrics, diskIO, networkIO)
                };

                CurrentMetrics = newMetrics;
                _loadHistory.Add(newMetrics);

                // 检查告警条件
                CheckAlertConditions(newMetrics);

                _logger.LogTrace("Metrics collected: {Metrics}", newMetrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting metrics");
            }
        }

        /// <summary>
        /// 获取磁盘I/O使用率
        /// </summary>
        private double GetDiskIOUsage()
        {
            try
            {
                // Windows环境使用PerformanceCounter
                if (_diskReadCounter != null && _diskWriteCounter != null)
                {
                    var readBytes = _diskReadCounter.NextValue();
                    var writeBytes = _diskWriteCounter.NextValue();

                    // 转换为MB/s并计算使用率（假设100MB/s为满负载）
                    var totalMBps = (readBytes + writeBytes) / (1024 * 1024);
                    return Math.Min(1.0, totalMBps / 100.0);
                }

                // 跨平台方法：返回较低的默认值
                return 0.05; // 5%的磁盘使用率
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "Error getting disk I/O usage");
                return 0.05;
            }
        }

        /// <summary>
        /// 获取网络I/O使用率
        /// </summary>
        private double GetNetworkIOUsage()
        {
            try
            {
                // Windows环境使用PerformanceCounter
                if (_networkSentCounter != null && _networkReceivedCounter != null)
                {
                    var sentBytes = _networkSentCounter.NextValue();
                    var receivedBytes = _networkReceivedCounter.NextValue();

                    // 转换为MB/s并计算使用率（假设100MB/s为满负载）
                    var totalMBps = (sentBytes + receivedBytes) / (1024 * 1024);
                    return Math.Min(1.0, totalMBps / 100.0);
                }

                // 跨平台方法：返回较低的默认值
                return 0.03; // 3%的网络使用率
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "Error getting network I/O usage");
                return 0.03;
            }
        }

        /// <summary>
        /// 获取活跃工作流数量
        /// </summary>
        private int GetActiveWorkflowCount()
        {
            // 通过IWorkflowEngine获取实际的活跃工作流数量
            try
            {
                if (_workflowEngine != null)
                {
                    // 使用同步方法获取活跃执行数
                    var getActiveCountMethod = _workflowEngine.GetType().GetMethod("GetActiveExecutionCount");
                    if (getActiveCountMethod != null)
                    {
                        var result = getActiveCountMethod.Invoke(_workflowEngine, Array.Empty<object>());
                        if (result is int intResult)
                        {
                            return intResult;
                        }
                    }
                    
                    // 如果没有同步方法，尝试使用异步方法
                    var getActiveCountAsyncMethod = _workflowEngine.GetType().GetMethod("GetActiveExecutionCountAsync");
                    if (getActiveCountAsyncMethod != null)
                    {
                        var taskResult = getActiveCountAsyncMethod.Invoke(_workflowEngine, new object[] { default(CancellationToken) });
                        if (taskResult is Task<int> task)
                        {
                            // 注意：这里使用Result可能会导致死锁，但在这种监控场景下是可以接受的
                            // 或者可以使用GetAwaiter().GetResult()来避免死锁警告
                            return task.GetAwaiter().GetResult();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "Error getting active workflow count from WorkflowEngine");
            }
            
            return 0;
        }

        /// <summary>
        /// 获取当前背压状态
        /// </summary>
        private BackpressureState GetCurrentBackpressureState()
        {
            // 通过BackpressureController获取实际的背压状态
            try
            {
                if (_backpressureController != null)
                {
                    // 获取背压控制器的当前状态
                    // 这需要在BackpressureController中添加状态获取方法
                    return _backpressureController.GetCurrentState();
                }
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "Error getting backpressure state");
            }
            
            return BackpressureState.Normal;
        }

        /// <summary>
        /// 计算负载级别
        /// </summary>
        private LoadLevel CalculateLoadLevel(SystemMetrics systemMetrics, double diskIO, double networkIO)
        {
            var overallLoad = (systemMetrics.CpuUsage + systemMetrics.MemoryUsage + diskIO + networkIO) / 4.0;
            
            // 考虑队列长度和响应时间对负载的影响
            var queueImpact = Math.Min(1.0, systemMetrics.QueueLength / 100.0);
            var responseTimeImpact = Math.Min(1.0, systemMetrics.AverageResponseTimeMs / 1000.0);
            
            // 综合计算负载评分
            var compositeLoad = (overallLoad * 0.6) + (queueImpact * 0.2) + (responseTimeImpact * 0.2);
            
            if (compositeLoad >= _settings.CriticalLoadThreshold)
                return LoadLevel.Critical;
            else if (compositeLoad >= _settings.HighLoadThreshold)
                return LoadLevel.High;
            else if (compositeLoad >= _settings.MediumLoadThreshold)
                return LoadLevel.Medium;
            else
                return LoadLevel.Low;
        }

        /// <summary>
        /// 检查告警条件
        /// </summary>
        private void CheckAlertConditions(LoadMetrics metrics)
        {
            if (DateTime.UtcNow - _lastAlertTime < _alertCooldown)
                return;

            if (metrics.LoadLevel >= LoadLevel.Critical)
            {
                var alert = new LoadAlert
                {
                    Id = Guid.NewGuid(),
                    Timestamp = DateTime.UtcNow,
                    Level = AlertLevel.Critical,
                    Message = $"系统负载达到临界水平: {metrics.LoadLevel}",
                    Metrics = metrics
                };
                
                _activeAlerts.Enqueue(alert);
                _lastAlertTime = DateTime.UtcNow;
                
                _logger.LogWarning("Critical load alert: {Alert}", alert);
            }
        }

        /// <summary>
        /// 执行负载分析和预测
        /// </summary>
        private void PerformAnalysis(object state)
        {
            if (_disposed) return;

            try
            {
                lock (_analysisLock)
                {
                    // 生成负载预测
                    _currentPrediction = GenerateLoadPrediction();

                    // 生成优化建议
                    _currentRecommendation = GenerateOptimizationRecommendations();

                    // 清理过期告警
                    CleanupExpiredAlerts();
                }

                _logger.LogDebug("Load analysis completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing load analysis");
            }
        }

        /// <summary>
        /// 生成负载预测
        /// </summary>
        private LoadPrediction GenerateLoadPrediction()
        {
            var recentMetrics = _loadHistory.GetItems().TakeLast(Math.Min(60, _loadHistory.Count)).ToList();

            if (recentMetrics.Count < 5)
            {
                return new LoadPrediction
                {
                    Timestamp = DateTime.UtcNow,
                    PredictionHorizon = TimeSpan.FromMinutes(_settings.PredictionHorizonMinutes),
                    PredictedLoadLevel = CurrentMetrics.LoadLevel,
                    Confidence = 0.5,
                    Trend = LoadTrend.Stable,
                    EstimatedPeakTime = null,
                    RecommendedActions = new List<string> { "数据不足，无法进行准确预测" }
                };
            }

            // 简单的趋势分析
            var trend = AnalyzeTrend(recentMetrics);
            var predictedLevel = PredictLoadLevel(recentMetrics, trend);
            var confidence = CalculatePredictionConfidence(recentMetrics);
            var estimatedPeakTime = EstimatePeakTime(recentMetrics, trend);

            // 基于趋势生成预测动作
            var actions = GeneratePredictionActions(predictedLevel, trend);
            
            // 添加基于具体指标的预测建议
            if (CurrentMetrics.CpuUsage > 0.7)
            {
                actions.Add("预测CPU使用率可能继续上升，建议准备扩容");
            }
            
            if (CurrentMetrics.MemoryUsage > 0.7)
            {
                actions.Add("预测内存使用率可能继续上升，建议检查内存使用");
            }

            return new LoadPrediction
            {
                Timestamp = DateTime.UtcNow,
                PredictionHorizon = TimeSpan.FromMinutes(_settings.PredictionHorizonMinutes),
                PredictedLoadLevel = predictedLevel,
                Confidence = confidence,
                Trend = trend,
                EstimatedPeakTime = estimatedPeakTime,
                RecommendedActions = actions
            };
        }

        /// <summary>
        /// 预测负载级别
        /// </summary>
        private LoadLevel PredictLoadLevel(List<LoadMetrics> recentMetrics, LoadTrend trend)
        {
            if (recentMetrics.Count == 0) return CurrentMetrics.LoadLevel;

            // 计算最近指标的平均负载
            var avgLoad = recentMetrics.Average(m => m.LoadScore);
            
            // 根据趋势调整预测
            double trendAdjustment = 0;
            switch (trend)
            {
                case LoadTrend.Increasing:
                    trendAdjustment = 0.1;
                    break;
                case LoadTrend.Decreasing:
                    trendAdjustment = -0.1;
                    break;
                case LoadTrend.Stable:
                    trendAdjustment = 0;
                    break;
            }

            var predictedLoad = avgLoad + trendAdjustment;
            
            // 根据阈值确定预测的负载级别
            if (predictedLoad >= _settings.CriticalLoadThreshold)
                return LoadLevel.Critical;
            else if (predictedLoad >= _settings.HighLoadThreshold)
                return LoadLevel.High;
            else if (predictedLoad >= _settings.MediumLoadThreshold)
                return LoadLevel.Medium;
            else
                return LoadLevel.Low;
        }

        /// <summary>
        /// 计算预测置信度
        /// </summary>
        private double CalculatePredictionConfidence(List<LoadMetrics> recentMetrics)
        {
            if (recentMetrics.Count < 5) return 0.3;

            // 计算数据点的标准差，标准差越小，置信度越高
            var loads = recentMetrics.Select(m => m.LoadScore).ToList();
            var mean = loads.Average();
            var variance = loads.Select(x => Math.Pow(x - mean, 2)).Average();
            var stdDev = Math.Sqrt(variance);

            // 标准差越小，置信度越高（最大0.9）
            var stabilityFactor = Math.Max(0.1, 1.0 - stdDev);
            
            // 数据点越多，置信度越高
            var dataFactor = Math.Min(1.0, recentMetrics.Count / 30.0);
            
            // 综合置信度
            var confidence = (stabilityFactor + dataFactor) / 2.0;
            
            // 限制在最小和最大置信度之间
            return Math.Max(_settings.MinPredictionConfidence, Math.Min(0.95, confidence));
        }

        /// <summary>
        /// 估算峰值时间
        /// </summary>
        private DateTime? EstimatePeakTime(List<LoadMetrics> recentMetrics, LoadTrend trend)
        {
            if (recentMetrics.Count < 2) return null;

            var currentLoad = CurrentMetrics.LoadScore;
            
            switch (trend)
            {
                case LoadTrend.Increasing:
                    // 如果负载在增加，预测何时达到临界阈值
                    var increaseRate = CalculateAverageIncreaseRate(recentMetrics);
                    if (increaseRate > 0)
                    {
                        var loadToCritical = _settings.CriticalLoadThreshold - currentLoad;
                        var minutesToCritical = loadToCritical / increaseRate;
                        return DateTime.UtcNow.AddMinutes(Math.Max(1, minutesToCritical));
                    }
                    return DateTime.UtcNow.AddMinutes(_settings.PredictionHorizonMinutes);

                case LoadTrend.Decreasing:
                    // 如果负载在减少，预测何时达到低负载
                    var decreaseRate = CalculateAverageDecreaseRate(recentMetrics);
                    if (decreaseRate > 0)
                    {
                        var loadToLow = currentLoad - _settings.MediumLoadThreshold;
                        var minutesToLow = loadToLow / decreaseRate;
                        return DateTime.UtcNow.AddMinutes(Math.Max(1, minutesToLow));
                    }
                    return DateTime.UtcNow.AddMinutes(_settings.PredictionHorizonMinutes);

                case LoadTrend.Stable:
                default:
                    // 负载稳定，预测在预测时间范围内保持当前水平
                    return DateTime.UtcNow.AddMinutes(_settings.PredictionHorizonMinutes);
            }
        }

        /// <summary>
        /// 计算平均增长速率
        /// </summary>
        private double CalculateAverageIncreaseRate(List<LoadMetrics> metrics)
        {
            if (metrics.Count < 2) return 0;

            var increases = new List<double>();
            for (int i = 1; i < metrics.Count; i++)
            {
                var diff = metrics[i].LoadScore - metrics[i - 1].LoadScore;
                if (diff > 0)
                {
                    var timeDiff = (metrics[i].Timestamp - metrics[i - 1].Timestamp).TotalMinutes;
                    if (timeDiff > 0)
                    {
                        increases.Add(diff / timeDiff);
                    }
                }
            }

            return increases.Count > 0 ? increases.Average() : 0;
        }

        /// <summary>
        /// 计算平均下降速率
        /// </summary>
        private double CalculateAverageDecreaseRate(List<LoadMetrics> metrics)
        {
            if (metrics.Count < 2) return 0;

            var decreases = new List<double>();
            for (int i = 1; i < metrics.Count; i++)
            {
                var diff = metrics[i - 1].LoadScore - metrics[i].LoadScore;
                if (diff > 0)
                {
                    var timeDiff = (metrics[i].Timestamp - metrics[i - 1].Timestamp).TotalMinutes;
                    if (timeDiff > 0)
                    {
                        decreases.Add(diff / timeDiff);
                    }
                }
            }

            return decreases.Count > 0 ? decreases.Average() : 0;
        }

        /// <summary>
        /// 生成预测动作建议
        /// </summary>
        private List<string> GeneratePredictionActions(LoadLevel predictedLevel, LoadTrend trend)
        {
            var actions = new List<string>();

            switch (predictedLevel)
            {
                case LoadLevel.Critical:
                    actions.Add("预测系统将进入临界负载状态");
                    actions.Add("建议立即准备应急措施");
                    break;
                case LoadLevel.High:
                    actions.Add("预测系统将进入高负载状态");
                    actions.Add("建议监控资源使用情况");
                    break;
                case LoadLevel.Medium:
                    actions.Add("预测系统将保持中等负载");
                    break;
                case LoadLevel.Low:
                    actions.Add("预测系统将保持低负载状态");
                    break;
            }

            switch (trend)
            {
                case LoadTrend.Increasing:
                    actions.Add("预测负载将持续增长");
                    if (predictedLevel >= LoadLevel.High)
                    {
                        actions.Add("建议提前准备扩容");
                    }
                    break;
                case LoadTrend.Decreasing:
                    actions.Add("预测负载将逐渐降低");
                    break;
                case LoadTrend.Stable:
                    actions.Add("预测负载将保持稳定");
                    break;
            }

            return actions;
        }

        /// <summary>
        /// 生成优化建议
        /// </summary>
        private LoadRecommendation GenerateOptimizationRecommendations()
        {
            var recommendations = new List<OptimizationRecommendation>();
            var overallAssessment = "";

            // 基于当前负载状态生成建议
            switch (CurrentMetrics.LoadLevel)
            {
                case LoadLevel.Critical:
                    overallAssessment = "系统处于临界负载状态，请立即采取措施";
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Category = "Concurrency",
                        Description = "立即降低并发执行数",
                        Impact = "高",
                        Effort = "低",
                        ExpectedImprovement = "显著降低系统负载"
                    });
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Category = "Backpressure",
                        Description = "启用强背压控制",
                        Impact = "高",
                        Effort = "低",
                        ExpectedImprovement = "有效控制请求流入速度"
                    });
                    break;

                case LoadLevel.High:
                    overallAssessment = "系统处于高负载状态，建议优化资源配置";
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Category = "Concurrency",
                        Description = "适度降低并发执行数",
                        Impact = "中",
                        Effort = "低",
                        ExpectedImprovement = "缓解系统压力"
                    });
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Category = "Workflows",
                        Description = "检查长时间运行的工作流",
                        Impact = "中",
                        Effort = "中",
                        ExpectedImprovement = "优化资源使用效率"
                    });
                    break;

                case LoadLevel.Medium:
                    overallAssessment = "系统处于中等负载状态，可考虑优化";
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Category = "Monitoring",
                        Description = "持续监控系统状态",
                        Impact = "低",
                        Effort = "低",
                        ExpectedImprovement = "及时发现潜在问题"
                    });
                    break;

                case LoadLevel.Low:
                    overallAssessment = "系统处于低负载状态，资源利用率较低";
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Category = "Concurrency",
                        Description = "可适当提高并发执行数",
                        Impact = "中",
                        Effort = "低",
                        ExpectedImprovement = "提高系统吞吐量"
                    });
                    break;
            }

            // 基于具体指标生成建议
            if (CurrentMetrics.CpuUsage > 0.8)
            {
                overallAssessment += " CPU使用率过高，建议优化计算密集型任务";
                recommendations.Add(new OptimizationRecommendation
                {
                    Category = "CPU",
                    Description = "分析和优化CPU密集型节点",
                    Impact = "高",
                    Effort = "高",
                    ExpectedImprovement = "显著降低CPU使用率"
                });
            }

            if (CurrentMetrics.MemoryUsage > 0.8)
            {
                overallAssessment += " 内存使用率过高，建议检查内存泄漏";
                recommendations.Add(new OptimizationRecommendation
                {
                    Category = "Memory",
                    Description = "检查大对象分配和内存泄漏",
                    Impact = "高",
                    Effort = "中",
                    ExpectedImprovement = "降低内存使用率"
                });
            }

            if (CurrentMetrics.QueueLength > 50)
            {
                overallAssessment += " 执行队列过长，可能存在性能瓶颈";
                recommendations.Add(new OptimizationRecommendation
                {
                    Category = "Queue",
                    Description = "分析队列增长原因",
                    Impact = "中",
                    Effort = "中",
                    ExpectedImprovement = "减少队列等待时间"
                });
            }

            if (CurrentMetrics.AverageResponseTimeMs > 500)
            {
                overallAssessment += " 平均响应时间过长，建议优化慢节点";
                recommendations.Add(new OptimizationRecommendation
                {
                    Category = "Performance",
                    Description = "识别和优化慢节点",
                    Impact = "中",
                    Effort = "中",
                    ExpectedImprovement = "提高响应速度"
                });
            }

            return new LoadRecommendation
            {
                Timestamp = DateTime.UtcNow,
                Priority = GetRecommendationPriority(CurrentMetrics.LoadLevel),
                Recommendations = recommendations,
                OverallAssessment = overallAssessment,
                NextReviewTime = DateTime.UtcNow.AddMinutes(30)
            };
        }

        /// <summary>
        /// 根据负载级别获取建议优先级
        /// </summary>
        private RecommendationPriority GetRecommendationPriority(LoadLevel loadLevel)
        {
            return loadLevel switch
            {
                LoadLevel.Critical => RecommendationPriority.Critical,
                LoadLevel.High => RecommendationPriority.High,
                LoadLevel.Medium => RecommendationPriority.Medium,
                LoadLevel.Low => RecommendationPriority.Low,
                _ => RecommendationPriority.Low
            };
        }

        /// <summary>
        /// 计算推荐置信度
        /// </summary>
        private double CalculateRecommendationConfidence()
        {
            // 基于历史数据量计算置信度
            var historyCount = Math.Min(1.0, _loadHistory.Count / 100.0);
            var timeFactor = Math.Min(1.0, (DateTime.UtcNow - CurrentMetrics.Timestamp).TotalMinutes / 5.0);
            
            return Math.Min(0.95, (historyCount + timeFactor) / 2.0);
        }

        /// <summary>
        /// 分析负载趋势
        /// </summary>
        private LoadTrend AnalyzeTrend(List<LoadMetrics> metrics)
        {
            if (metrics.Count < 3) return LoadTrend.Stable;

            var recent = metrics.TakeLast(5).ToList();
            var older = metrics.Take(metrics.Count - 5).TakeLast(5).ToList();

            if (recent.Count == 0 || older.Count == 0) return LoadTrend.Stable;

            var recentAvg = recent.Average(m => (m.CpuUsage + m.MemoryUsage) / 2.0);
            var olderAvg = older.Average(m => (m.CpuUsage + m.MemoryUsage) / 2.0);

            var difference = recentAvg - olderAvg;
            var threshold = 0.05; // 5%的变化阈值

            if (difference > threshold)
                return LoadTrend.Increasing;
            else if (difference < -threshold)
                return LoadTrend.Decreasing;
            else
                return LoadTrend.Stable;
        }

        /// <summary>
        /// 清理过期告警
        /// </summary>
        private void CleanupExpiredAlerts()
        {
            while (_activeAlerts.TryPeek(out var alert) && 
                   DateTime.UtcNow - alert.Timestamp > TimeSpan.FromHours(1))
            {
                _activeAlerts.TryDequeue(out _);
            }
        }

        /// <summary>
        /// 获取历史负载数据
        /// </summary>
        /// <param name="period">时间周期</param>
        /// <returns>历史负载数据</returns>
        public List<LoadMetrics> GetLoadHistory(TimeSpan period)
        {
            var cutoffTime = DateTime.UtcNow - period;
            return _loadHistory.GetItems()
                .Where(m => m.Timestamp >= cutoffTime)
                .OrderBy(m => m.Timestamp)
                .ToList();
        }

        /// <summary>
        /// 获取负载趋势分析
        /// </summary>
        /// <param name="period">分析时间段</param>
        /// <returns>趋势分析结果</returns>
        public LoadTrendAnalysis GetLoadTrendAnalysis(TimeSpan period)
        {
            var history = GetLoadHistory(period);
            if (history.Count < 2)
            {
                return new LoadTrendAnalysis
                {
                    Period = period,
                    DataPoints = history.Count,
                    Trend = LoadTrend.Stable,
                    AverageLoad = 0,
                    PeakLoad = 0,
                    MinLoad = 0,
                    Variance = 0
                };
            }

            var loads = history.Select(h => (h.CpuUsage + h.MemoryUsage) / 2.0).ToList();
            var trend = AnalyzeTrend(history);

            return new LoadTrendAnalysis
            {
                Period = period,
                DataPoints = history.Count,
                Trend = trend,
                AverageLoad = loads.Average(),
                PeakLoad = loads.Max(),
                MinLoad = loads.Min(),
                Variance = CalculateLoadVariance(history)
            };
        }

        /// <summary>
        /// 计算负载方差
        /// </summary>
        private double CalculateLoadVariance(List<LoadMetrics> metrics)
        {
            if (metrics.Count < 2) return 0;

            var loads = metrics.Select(m => m.LoadScore).ToList();
            var mean = loads.Average();
            var variance = loads.Select(x => Math.Pow(x - mean, 2)).Average();
            return variance;
        }

        /// <summary>
        /// 手动触发分析
        /// </summary>
        public void TriggerAnalysis()
        {
            if (_disposed) return;

            Task.Run(() => PerformAnalysis(null!));
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;

            _monitoringTimer?.Dispose();
            _analysisTimer?.Dispose();

            _diskReadCounter?.Dispose();
            _diskWriteCounter?.Dispose();
            _networkSentCounter?.Dispose();
            _networkReceivedCounter?.Dispose();

            _logger.LogInformation("LoadMonitor disposed");
        }
    }
}