# FlowCustomV1 命名一致性修复脚本
# 统一 IsBuiltin vs IsBuiltIn 的命名问题

Write-Host "🔧 FlowCustomV1 命名一致性修复" -ForegroundColor Green
Write-Host "统一 IsBuiltin vs IsBuiltIn 命名问题" -ForegroundColor Yellow
Write-Host ""

# 定义需要检查和修复的文件
$filesToCheck = @(
    "src/FlowCustomV1.Core/Models/ParameterTemplate.cs",
    "src/FlowCustomV1.Core/Models/TemplateDefinition.cs", 
    "src/FlowCustomV1.Core/DTOs/TemplateDto.cs",
    "src/FlowCustomV1.Core/Services/ParameterTemplateService.cs",
    "src/FlowCustomV1.Core/Services/TemplateService.cs.bak",
    "src/FlowCustomV1.Core/Services/TemplateDataMigration.cs"
)

# 统计信息
$totalFiles = 0
$modifiedFiles = 0
$totalReplacements = 0

Write-Host "📋 检查以下文件中的命名不一致问题:" -ForegroundColor Cyan
foreach ($file in $filesToCheck) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
        $totalFiles++
    } else {
        Write-Host "  ⚠️  $file (文件不存在)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🔍 分析当前命名使用情况..." -ForegroundColor Cyan

# 分析每个文件的使用情况
foreach ($file in $filesToCheck) {
    if (-not (Test-Path $file)) { continue }
    
    $content = Get-Content $file -Raw
    $isBuiltinCount = ([regex]::Matches($content, "IsBuiltin")).Count
    $isBuiltInCount = ([regex]::Matches($content, "IsBuiltIn")).Count
    
    if ($isBuiltinCount -gt 0 -or $isBuiltInCount -gt 0) {
        Write-Host "📄 ${file}:" -ForegroundColor White
        $builtinColor = if($isBuiltinCount -gt 0){"Green"}else{"Gray"}
        $builtInColor = if($isBuiltInCount -gt 0){"Yellow"}else{"Gray"}
        Write-Host "    IsBuiltin: $isBuiltinCount 次" -ForegroundColor $builtinColor
        Write-Host "    IsBuiltIn: $isBuiltInCount 次" -ForegroundColor $builtInColor
    }
}

Write-Host ""
Write-Host "🎯 修复策略: 统一使用 'IsBuiltIn' (大写I)" -ForegroundColor Cyan
Write-Host "原因: 符合C#属性命名约定 (PascalCase)" -ForegroundColor Gray
Write-Host ""

$response = Read-Host "是否继续执行修复? (y/N)"
if ($response -ne "y" -and $response -ne "Y") {
    Write-Host "❌ 用户取消操作" -ForegroundColor Red
    exit 0
}

Write-Host ""
Write-Host "🔧 开始修复..." -ForegroundColor Green

# 执行修复
foreach ($file in $filesToCheck) {
    if (-not (Test-Path $file)) { continue }
    
    $content = Get-Content $file -Raw
    $originalContent = $content
    
    # 替换 IsBuiltin 为 IsBuiltIn (但要避免替换已经是 IsBuiltIn 的)
    # 使用负向前瞻来避免替换 IsBuiltIn 中的 IsBuilt
    $content = $content -replace "IsBuiltin(?!In)", "IsBuiltIn"
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file -Value $content -Encoding UTF8
        $replacements = ([regex]::Matches($originalContent, "IsBuiltin(?!In)")).Count
        Write-Host "  ✅ ${file} - 修复了 $replacements 处" -ForegroundColor Green
        $modifiedFiles++
        $totalReplacements += $replacements
    } else {
        Write-Host "  ⚪ ${file} - 无需修复" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "📊 修复完成统计:" -ForegroundColor Cyan
Write-Host "  检查文件: $totalFiles" -ForegroundColor White
Write-Host "  修改文件: $modifiedFiles" -ForegroundColor Green
Write-Host "  总替换数: $totalReplacements" -ForegroundColor Yellow

if ($totalReplacements -gt 0) {
    Write-Host ""
    Write-Host "⚠️  重要提醒:" -ForegroundColor Yellow
    Write-Host "1. 需要重新生成数据库迁移文件" -ForegroundColor Red
    Write-Host "2. 需要重新构建并重启API服务" -ForegroundColor Red
    Write-Host "3. 建议运行测试确保功能正常" -ForegroundColor Red
    
    Write-Host ""
    Write-Host "🚀 建议执行以下命令:" -ForegroundColor Cyan
    Write-Host "   powershell -ExecutionPolicy Bypass -File migrate-to-mysql.ps1 -SkipDataMigration" -ForegroundColor White
    Write-Host "   docker-compose -f docker-compose.mysql.yml restart api" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "🎉 所有文件命名已经一致，无需修复！" -ForegroundColor Green
}
