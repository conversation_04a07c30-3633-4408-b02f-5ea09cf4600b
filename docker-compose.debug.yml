version: '3.8'

services:
  # NATS 消息服务器
  nats:
    image: nats:2.10-alpine
    container_name: flowcustom-nats-debug
    ports:
      - "4222:4222"
      - "8222:8222"
      - "6222:6222"
    command: [
      "--jetstream",
      "--store_dir=/data",
      "--http_port=8222"
    ]
    volumes:
      - nats_data_debug:/data
    networks:
      - flowcustom_debug
    restart: unless-stopped

  # API 服务 (调试版本)
  api:
    build:
      context: .
      dockerfile: src/FlowCustomV1.Api/Dockerfile.debug
    container_name: flowcustom-api-debug
    ports:
      - "5279:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - Database__Provider=SQLite
      - ConnectionStrings__SQLite=Data Source=/app/data/flowcustom.db
      - Nats__Servers__0=nats://nats:4222
      - Logging__LogLevel__Default=Debug
      - Logging__LogLevel__Microsoft=Information
      - Logging__LogLevel__Microsoft.AspNetCore=Information
    volumes:
      - api_data_debug:/app/data
      - api_logs_debug:/app/logs
      - api_plugins_debug:/app/plugins
    depends_on:
      - nats
    networks:
      - flowcustom_debug
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/api/test/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

volumes:
  nats_data_debug:
    driver: local
  api_data_debug:
    driver: local
  api_logs_debug:
    driver: local
  api_plugins_debug:
    driver: local

networks:
  flowcustom_debug:
    driver: bridge
