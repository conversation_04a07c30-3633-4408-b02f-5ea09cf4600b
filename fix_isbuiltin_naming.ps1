# FlowCustomV1 IsBuiltin/IsBuiltIn Naming Fix Script

Write-Host "Fixing IsBuiltin vs IsBuiltIn naming consistency..." -ForegroundColor Green

# Files to check and fix
$files = @(
    "src/FlowCustomV1.Core/Models/ParameterTemplate.cs",
    "src/FlowCustomV1.Core/Models/TemplateDefinition.cs", 
    "src/FlowCustomV1.Core/DTOs/TemplateDto.cs",
    "src/FlowCustomV1.Core/Services/ParameterTemplateService.cs"
)

$totalReplacements = 0
$modifiedFiles = 0

foreach ($file in $files) {
    if (-not (Test-Path $file)) {
        Write-Host "File not found: $file" -ForegroundColor Yellow
        continue
    }
    
    $content = Get-Content $file -Raw -Encoding UTF8
    $originalContent = $content
    
    # Count current usage
    $isBuiltinMatches = [regex]::Matches($content, "IsBuiltin(?!In)")
    $isBuiltInMatches = [regex]::Matches($content, "IsBuiltIn")
    
    Write-Host "File: $file" -ForegroundColor Cyan
    Write-Host "  IsBuiltin: $($isBuiltinMatches.Count)" -ForegroundColor White
    Write-Host "  IsBuiltIn: $($isBuiltInMatches.Count)" -ForegroundColor White
    
    # Replace IsBuiltin with IsBuiltIn (but not if it's already IsBuiltIn)
    $content = $content -replace "IsBuiltin(?!In)", "IsBuiltIn"
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file -Value $content -Encoding UTF8
        $replacements = $isBuiltinMatches.Count
        Write-Host "  Fixed: $replacements replacements" -ForegroundColor Green
        $modifiedFiles++
        $totalReplacements += $replacements
    } else {
        Write-Host "  No changes needed" -ForegroundColor Gray
    }
    Write-Host ""
}

Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "  Modified files: $modifiedFiles" -ForegroundColor Green
Write-Host "  Total replacements: $totalReplacements" -ForegroundColor Yellow

if ($totalReplacements -gt 0) {
    Write-Host ""
    Write-Host "IMPORTANT: You need to regenerate database migrations!" -ForegroundColor Red
    Write-Host "Run: powershell -ExecutionPolicy Bypass -File migrate-to-mysql.ps1 -SkipDataMigration" -ForegroundColor Yellow
}
