using Microsoft.EntityFrameworkCore;
using FlowCustomV1.Data;
using FlowCustomV1.Data.Providers;

namespace FlowCustomV1.Api.Services;

/// <summary>
/// 数据库初始化服务 - 负责系统启动时的数据库结构检查和创建
/// </summary>
public class DatabaseInitializationService
{
    private readonly ILogger<DatabaseInitializationService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IDatabaseProvider _databaseProvider;

    public DatabaseInitializationService(
        ILogger<DatabaseInitializationService> logger,
        IServiceProvider serviceProvider,
        IDatabaseProvider databaseProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _databaseProvider = databaseProvider;
    }

    /// <summary>
    /// 初始化数据库，包含完整的检查和创建流程
    /// </summary>
    public async Task InitializeAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<FlowCustomDbContext>();
        var configuration = scope.ServiceProvider.GetRequiredService<IConfiguration>();

        try
        {
            _logger.LogInformation("🚀 开始数据库初始化流程...");
            _logger.LogInformation("📊 使用数据库提供者: {ProviderName}", _databaseProvider.ProviderName);

            // 1. 获取连接字符串
            var connectionString = GetConnectionString(configuration);
            _logger.LogInformation("🔗 连接字符串验证: {IsValid}", _databaseProvider.ValidateConnectionString(connectionString) ? "有效" : "无效");

            // 2. 检查并创建数据库
            await EnsureDatabaseExistsAsync(connectionString);

            // 3. 检查数据库连接
            await CheckDatabaseConnectionAsync(context);

            // 4. 处理数据库迁移
            await HandleDatabaseMigrationsAsync(context);

            // 5. 验证表结构
            await ValidateTableStructureAsync(context);

            // 6. 初始化基础数据
            await InitializeBaseDataAsync(context);

            _logger.LogInformation("🎉 数据库初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 数据库初始化失败");
            throw;
        }
    }

    /// <summary>
    /// 获取连接字符串
    /// </summary>
    private string GetConnectionString(IConfiguration configuration)
    {
        return _databaseProvider.ProviderName.ToUpper() switch
        {
            "MYSQL" => configuration.GetConnectionString("MySQL") ?? _databaseProvider.GetConnectionStringTemplate(),
            "POSTGRESQL" => configuration.GetConnectionString("PostgreSQL") ?? _databaseProvider.GetConnectionStringTemplate(),
            "SQLITE" => configuration.GetConnectionString("DefaultConnection") ?? _databaseProvider.GetConnectionStringTemplate(),
            _ => _databaseProvider.GetConnectionStringTemplate()
        };
    }

    /// <summary>
    /// 确保数据库存在，如果不存在则创建
    /// </summary>
    private async Task EnsureDatabaseExistsAsync(string connectionString)
    {
        try
        {
            _logger.LogInformation("🔍 检查数据库是否存在...");

            var databaseExists = await _databaseProvider.DatabaseExistsAsync(connectionString);

            if (!databaseExists)
            {
                _logger.LogInformation("🆕 数据库不存在，正在创建...");
                await _databaseProvider.CreateDatabaseIfNotExistsAsync(connectionString);
                _logger.LogInformation("✅ 数据库创建成功");
            }
            else
            {
                _logger.LogInformation("✅ 数据库已存在");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 数据库存在性检查失败，将在连接时处理: {Message}", ex.Message);
        }
    }

    /// <summary>
    /// 检查数据库连接
    /// </summary>
    private async Task CheckDatabaseConnectionAsync(FlowCustomDbContext context)
    {
        _logger.LogInformation("🔍 检查数据库连接...");
        
        try
        {
            var canConnect = await context.Database.CanConnectAsync();
            if (canConnect)
            {
                _logger.LogInformation("✅ 数据库连接正常");
            }
            else
            {
                _logger.LogWarning("⚠️ 数据库连接失败，将尝试创建数据库");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 数据库连接检查异常: {Message}", ex.Message);
        }
    }

    /// <summary>
    /// 处理数据库迁移
    /// </summary>
    private async Task HandleDatabaseMigrationsAsync(FlowCustomDbContext context)
    {
        try
        {
            _logger.LogInformation("🔄 检查数据库迁移状态...");
            
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            var appliedMigrations = await context.Database.GetAppliedMigrationsAsync();
            
            _logger.LogInformation("📊 迁移状态: 已应用 {Applied} 个，待应用 {Pending} 个", 
                appliedMigrations.Count(), pendingMigrations.Count());

            if (pendingMigrations.Any())
            {
                _logger.LogInformation("🔄 应用待处理的迁移...");
                foreach (var migration in pendingMigrations)
                {
                    _logger.LogInformation("  - 应用迁移: {Migration}", migration);
                }
                
                await context.Database.MigrateAsync();
                _logger.LogInformation("✅ 数据库迁移完成");
            }
            else if (!appliedMigrations.Any())
            {
                // 全新数据库，使用EnsureCreated
                _logger.LogInformation("🆕 检测到全新数据库，创建表结构...");
                var created = await context.Database.EnsureCreatedAsync();
                
                if (created)
                {
                    _logger.LogInformation("✅ 数据库表结构创建完成");
                }
                else
                {
                    _logger.LogInformation("ℹ️ 数据库表结构已存在");
                }
            }
            else
            {
                _logger.LogInformation("✅ 数据库已是最新版本");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 数据库迁移失败，尝试备用方案...");
            
            // 备用方案：强制创建
            try
            {
                await context.Database.EnsureCreatedAsync();
                _logger.LogInformation("✅ 备用方案执行成功");
            }
            catch (Exception fallbackEx)
            {
                _logger.LogError(fallbackEx, "❌ 备用方案也失败");
                throw new InvalidOperationException("数据库初始化完全失败", ex);
            }
        }
    }

    /// <summary>
    /// 验证表结构完整性
    /// </summary>
    private async Task ValidateTableStructureAsync(FlowCustomDbContext context)
    {
        _logger.LogInformation("🔍 验证数据库表结构...");
        
        var requiredTables = GetRequiredTables();
        var missingTables = new List<string>();
        
        foreach (var tableName in requiredTables)
        {
            try
            {
                var exists = await CheckTableExistsAsync(context, tableName);
                if (exists)
                {
                    _logger.LogDebug("✅ 表 {TableName} 存在", tableName);
                }
                else
                {
                    _logger.LogWarning("❌ 表 {TableName} 不存在", tableName);
                    missingTables.Add(tableName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ 无法验证表 {TableName}", tableName);
                missingTables.Add(tableName);
            }
        }
        
        if (missingTables.Any())
        {
            _logger.LogWarning("⚠️ 发现缺失表: {Tables}", string.Join(", ", missingTables));
            
            // 尝试重新创建
            _logger.LogInformation("🔧 重新创建缺失的表...");
            await context.Database.EnsureCreatedAsync();
            _logger.LogInformation("✅ 表结构重建完成");
        }
        else
        {
            _logger.LogInformation("✅ 所有必需表都存在");
        }
    }

    /// <summary>
    /// 检查表是否存在
    /// </summary>
    private async Task<bool> CheckTableExistsAsync(FlowCustomDbContext context, string tableName)
    {
        try
        {
            var sql = context.Database.IsSqlite() 
                ? $"SELECT name FROM sqlite_master WHERE type='table' AND name='{tableName}'"
                : $"SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{tableName}'";
                
            var result = await context.Database.SqlQueryRaw<string>(sql).ToListAsync();
            return result.Any();
        }
        catch
        {
            // 如果查询失败，假设表不存在
            return false;
        }
    }

    /// <summary>
    /// 获取必需的表列表
    /// </summary>
    private static string[] GetRequiredTables()
    {
        return new[]
        {
            "WorkflowDefinitions",
            "WorkflowExecutions",
            "WorkflowVersions", 
            "NodeExecutions",
            "ExecutionLogs",
            "PluginDefinitions",
            "ParameterTemplates",
            "TemplateDefinitions",
            "TemplateCategories",
            "TemplateInstances",
            "TemplateUsageStatistics"
        };
    }

    /// <summary>
    /// 初始化基础数据
    /// </summary>
    private async Task InitializeBaseDataAsync(FlowCustomDbContext context)
    {
        _logger.LogInformation("🔧 检查基础数据...");
        
        try
        {
            // 检查是否需要初始化基础数据
            var hasWorkflows = await context.WorkflowDefinitions.AnyAsync();
            var hasTemplates = await context.TemplateDefinitions.AnyAsync();
            
            if (!hasWorkflows && !hasTemplates)
            {
                _logger.LogInformation("ℹ️ 检测到空数据库，基础数据将在后续流程中初始化");
            }
            else
            {
                _logger.LogInformation("✅ 基础数据检查完成");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 基础数据检查异常，但不影响系统启动");
        }
    }
}
