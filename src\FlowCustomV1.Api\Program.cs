using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Services;
using FlowCustomV1.Core.Events;
using FlowCustomV1.Data;
using FlowCustomV1.Data.Providers;
using FlowCustomV1.Data.Repositories;
using FlowCustomV1.Engine;
using FlowCustomV1.Engine.Configuration;
using FlowCustomV1.Engine.Services;
using FlowCustomV1.PluginHost;
using FlowCustomV1.Api.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using FlowCustomV1.Api.Configuration;
using FlowCustomV1.Core.Logging;
using FlowCustomV1.Api.Logging;
using System.Text;

// 设置字符编码以支持中文
try
{
    Console.WriteLine("[STARTUP] 开始设置字符编码...");
    Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
    Console.OutputEncoding = Encoding.UTF8;
    Console.InputEncoding = Encoding.UTF8;
    Console.WriteLine("[STARTUP] 字符编码设置完成");
}
catch (Exception ex)
{
    Console.WriteLine($"[STARTUP ERROR] 字符编码设置失败: {ex.Message}");
}

Console.WriteLine("[STARTUP] 开始创建WebApplication Builder...");
var builder = WebApplication.CreateBuilder(args);
Console.WriteLine("[STARTUP] WebApplication Builder创建完成");

// 配置Serilog
SerilogConfiguration.ConfigureSerilog(builder.Configuration, builder.Environment.IsDevelopment());
builder.Host.UseSerilog();

// 添加Core层日志服务
builder.Services.AddCoreLogging(builder.Configuration);

// Add services to the container
builder.Services.AddControllers(options =>
{
    // 添加模型绑定日志
    options.ModelBindingMessageProvider.SetValueMustNotBeNullAccessor(field => $"字段 '{field}' 不能为空");
    options.ModelBindingMessageProvider.SetValueIsInvalidAccessor(value => $"值 '{value}' 无效");
    options.ModelBindingMessageProvider.SetValueMustBeANumberAccessor(field => $"字段 '{field}' 必须是数字");
})
.ConfigureApiBehaviorOptions(options =>
{
    // 自定义模型验证失败的响应
    options.InvalidModelStateResponseFactory = context =>
    {
        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
        Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(logger, "模型绑定失败，详细错误:");

        foreach (var error in context.ModelState)
        {
            Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(logger, "字段: {Field}, 错误: {Errors}",
                error.Key,
                string.Join(", ", error.Value?.Errors.Select(e => e.ErrorMessage) ?? new string[0]));
        }

        return new BadRequestObjectResult(new
        {
            message = "模型验证失败",
            errors = context.ModelState.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
            )
        });
    };
});
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add Database Provider
Console.WriteLine("[STARTUP] 开始配置数据库提供程序...");
var databaseProvider = builder.Configuration.GetValue<string>("Database:Provider") ?? "SQLite";
Console.WriteLine($"[STARTUP] 数据库提供程序: {databaseProvider}");
switch (databaseProvider.ToUpper())
{
    case "MYSQL":
        builder.Services.AddSingleton<IDatabaseProvider, MySqlProvider>();
        builder.Services.AddDbContext<FlowCustomDbContext>((serviceProvider, options) =>
        {
            var provider = serviceProvider.GetRequiredService<IDatabaseProvider>();
            var connectionString = builder.Configuration.GetConnectionString("MySQL")
                ?? throw new InvalidOperationException("MySQL connection string is required when using MySQL provider");
            provider.ConfigureDbContext(options, connectionString);
        });
        break;

    case "POSTGRESQL":
    case "POSTGRES":
        builder.Services.AddSingleton<IDatabaseProvider, PostgreSqlProvider>();
        builder.Services.AddDbContext<FlowCustomDbContext>((serviceProvider, options) =>
        {
            var provider = serviceProvider.GetRequiredService<IDatabaseProvider>();
            var connectionString = builder.Configuration.GetConnectionString("PostgreSQL")
                ?? throw new InvalidOperationException("PostgreSQL connection string is required when using PostgreSQL provider");
            provider.ConfigureDbContext(options, connectionString);
        });
        break;

    case "SQLITE":
    default:
        builder.Services.AddSingleton<IDatabaseProvider, SqliteProvider>();
        builder.Services.AddDbContext<FlowCustomDbContext>((serviceProvider, options) =>
        {
            var provider = serviceProvider.GetRequiredService<IDatabaseProvider>();
            var connectionString = builder.Configuration.GetConnectionString("DefaultConnection")
                ?? "Data Source=flowcustom.db";
            provider.ConfigureDbContext(options, connectionString);
        });
        break;
}

// Add repositories
builder.Services.AddScoped<IWorkflowRepository, WorkflowRepository>();
builder.Services.AddScoped<IWorkflowExecutionRepository, WorkflowExecutionRepository>();
builder.Services.AddScoped<IWorkflowVersionRepository, WorkflowVersionRepository>();
builder.Services.AddScoped<IWorkflowVersionService, WorkflowVersionService>();
// builder.Services.AddScoped<ITemplateRepository, TemplateRepository>(); // 暂时禁用，有编译错误

// Add HTTP client
builder.Services.AddHttpClient();

// Add plugin optimization configuration
builder.Services.AddSingleton(provider =>
{
    var environment = provider.GetRequiredService<IWebHostEnvironment>();
    return environment.IsDevelopment()
        ? FlowCustomV1.PluginHost.Configuration.PluginOptimizationConfiguration.GetDevelopment()
        : FlowCustomV1.PluginHost.Configuration.PluginOptimizationConfiguration.GetHighPerformance();
});

// Add plugin code generation services
builder.Services.AddSingleton<FlowCustomV1.PluginHost.CodeGeneration.PluginCodeGenerator>();
builder.Services.AddSingleton<FlowCustomV1.PluginHost.CodeGeneration.CodeGenerationService>();

// Add plugin management
builder.Services.AddSingleton<IPluginManager, UnifiedPluginManager>();

// Add workflow engine configuration
builder.Services.AddSingleton(provider =>
{
    var environment = provider.GetRequiredService<IWebHostEnvironment>();
    return environment.IsDevelopment()
        ? FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetDevelopment()
        : FlowCustomV1.Engine.Configuration.WorkflowEngineConfiguration.GetHighPerformance();
});

// Add event publisher for node status events
builder.Services.AddSingleton<INodeStatusEventPublisher, NodeStatusEventPublisher>();

// Add core services - 工作流引擎必须是单例，确保执行队列持续运行
builder.Services.AddSingleton<IWorkflowEngine, WorkflowEngine>();
// builder.Services.AddScoped<ITemplateService, TemplateService>(); // 暂时禁用，有编译错误
builder.Services.AddSingleton<IProtocolTemplateService, ProtocolTemplateService>();
builder.Services.AddSingleton<IParameterTemplateService, ParameterTemplateService>();

// Add execution services (前后端分离新增)
builder.Services.AddScoped<IParameterValidationService, ParameterValidationService>();

// Add performance monitoring (P2性能监控)
builder.Services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();

// Add plugin caching (P2插件缓存)
builder.Services.AddSingleton<IPluginCacheService, PluginCacheService>();

// Add structured logging (P2结构化日志)
builder.Services.AddSingleton<IStructuredLoggingService, StructuredLoggingService>();

// Add load monitoring (负载监控)
FlowCustomV1.Engine.Monitoring.LoadMonitorIntegration.ConfigureLoadMonitoring(builder.Services);

// Add enhanced load monitoring (Task 1.5: 增强负载监控)
builder.Services.AddSingleton<FlowCustomV1.Engine.Concurrency.LoadMonitorSettings>(provider =>
{
    return new FlowCustomV1.Engine.Concurrency.LoadMonitorSettings
    {
        CollectionIntervalSeconds = 5,
        AnalysisIntervalSeconds = 60,
        HistoryCapacity = 1440, // 24小时
        HourlyHistoryCapacity = 720, // 30天
        DailyHistoryCapacity = 365, // 1年
        MediumLoadThreshold = 0.4,
        HighLoadThreshold = 0.7,
        CriticalLoadThreshold = 0.9,
        EnablePrediction = true,
        EnableAlerts = true,
        EnableRecommendations = true,
        AlertCooldownMinutes = 5,
        PredictionHorizonMinutes = 15,
        MinPredictionConfidence = 0.3
    };
});

builder.Services.AddSingleton<FlowCustomV1.Engine.Concurrency.LoadMonitor>(provider =>
{
    var systemMetricsCollector = provider.GetRequiredService<FlowCustomV1.Engine.Concurrency.SystemMetricsCollector>();
    var settings = provider.GetRequiredService<FlowCustomV1.Engine.Concurrency.LoadMonitorSettings>();
    var logger = provider.GetRequiredService<ILogger<FlowCustomV1.Engine.Concurrency.LoadMonitor>>();
    var workflowEngine = provider.GetService<IWorkflowEngine>(); // 可选依赖
    var backpressureController = provider.GetService<FlowCustomV1.Engine.Concurrency.BackpressureController>(); // 可选依赖

    return new FlowCustomV1.Engine.Concurrency.LoadMonitor(
        systemMetricsCollector, 
        settings, 
        logger,
        workflowEngine,
        backpressureController);
});

// Add high-throughput data pipeline (高吞吐量数据管道)
builder.Services.AddSingleton<FlowCustomV1.Engine.DataPipeline.IDataPartitioner, FlowCustomV1.Engine.DataPipeline.HashBasedDataPartitioner>();
builder.Services.AddSingleton<FlowCustomV1.Engine.DataPipeline.IParallelProcessor, FlowCustomV1.Engine.DataPipeline.HighPerformanceParallelProcessor>();
builder.Services.AddSingleton(typeof(FlowCustomV1.Engine.DataPipeline.IDataStreamProcessor<>), typeof(FlowCustomV1.Engine.DataPipeline.HighThroughputDataProcessor<>));

// Add executor factories (执行器工厂)
builder.Services.AddSingleton<ILocalExecutorFactory, FlowCustomV1.Engine.Services.LocalExecutorFactory>();
builder.Services.AddSingleton<IClusterExecutorFactory, FlowCustomV1.Engine.Services.ClusterExecutorFactory>();

// Add execution mode selector (执行模式选择器)
builder.Services.AddSingleton<IExecutionModeSelector, FlowCustomV1.Engine.Services.ExecutionModeSelector>();

// Add logging
builder.Services.AddLogging();

// Add database initialization service
builder.Services.AddScoped<DatabaseInitializationService>();

// Add NATS messaging service
builder.Services.AddSingleton<INatsService, NatsService>();

// Add frontend log service
builder.Services.AddSingleton<IFrontendLogService, FrontendLogService>();

// Add backend log NATS publisher service
builder.Services.AddSingleton<IBackendLogNatsPublisher, BackendLogNatsPublisher>();

// Add log cleanup background service
builder.Services.AddHostedService<LogCleanupService>();

// Add node status NATS publisher service
builder.Services.AddHostedService<NodeStatusNatsPublisher>();

Console.WriteLine("[STARTUP] 开始构建应用程序...");
var app = builder.Build();
Console.WriteLine("[STARTUP] 应用程序构建完成");

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

// 添加优化的请求日志中间件
app.Use(async (context, next) =>
{
    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();

    // 只在Debug级别记录详细信息
    Microsoft.Extensions.Logging.LoggerExtensions.LogDebug(logger, "收到请求: {Method} {Path}{QueryString}",
        context.Request.Method, context.Request.Path, context.Request.QueryString);

    await next();

    stopwatch.Stop();

    // 记录请求完成信息（包含性能数据）
    Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "请求完成: {Method} {Path} - {StatusCode} - {Duration}ms",
        context.Request.Method,
        context.Request.Path,
        context.Response.StatusCode,
        stopwatch.ElapsedMilliseconds);

    // 记录慢请求
    if (stopwatch.ElapsedMilliseconds > 1000)
    {
        Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(logger, "慢请求检测: {Method} {Path} - {Duration}ms",
            context.Request.Method, context.Request.Path, stopwatch.ElapsedMilliseconds);
    }
});

app.UseAuthorization();

// 添加健康检查端点
app.MapGet("/api/health", () => new { status = "healthy", timestamp = DateTime.UtcNow });

app.MapControllers();

// 后台初始化数据库和插件管理器（不阻塞应用启动）
_ = Task.Run(async () =>
{
    try
    {
        using var scope = app.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<FlowCustomDbContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "开始初始化数据库和插件管理器...");

        // 使用专门的数据库初始化服务
        var dbInitService = scope.ServiceProvider.GetRequiredService<DatabaseInitializationService>();
        await dbInitService.InitializeAsync();

        // 初始化插件管理器
        try
        {
            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "获取插件管理器服务...");
            var pluginManager = scope.ServiceProvider.GetRequiredService<IPluginManager>();
            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "插件管理器服务获取成功: {Type}", pluginManager.GetType().Name);

            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "开始初始化插件管理器...");
            if (pluginManager is UnifiedPluginManager unifiedManager)
            {
                await unifiedManager.InitializeAsync();
                Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "插件管理器初始化完成");
            }
            else
            {
                Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "插件管理器不支持初始化，跳过");
            }
        }
        catch (Exception pluginEx)
        {
            Microsoft.Extensions.Logging.LoggerExtensions.LogError(logger, pluginEx, "获取或初始化插件管理器失败");
        }

        // 初始化协议模板
        try
        {
            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "开始初始化协议模板...");
            var protocolTemplateService = scope.ServiceProvider.GetRequiredService<IProtocolTemplateService>();
            await protocolTemplateService.InitializeDefaultTemplates();
            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "协议模板初始化完成");
        }
        catch (Exception protocolEx)
        {
            Microsoft.Extensions.Logging.LoggerExtensions.LogError(logger, protocolEx, "初始化协议模板失败");
        }

        // 启动NATS服务
        try
        {
            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "开始启动NATS服务...");
            var natsService = scope.ServiceProvider.GetRequiredService<INatsService>();
            await natsService.StartAsync();
            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "NATS服务启动完成");

            // 配置NATS日志发布
            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "配置NATS日志发布...");
            ConfigureNatsLogging(app.Services);
            Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "NATS日志发布配置完成");
        }
        catch (Exception natsEx)
        {
            Microsoft.Extensions.Logging.LoggerExtensions.LogError(logger, natsEx, "启动NATS服务失败");
        }

        Microsoft.Extensions.Logging.LoggerExtensions.LogInformation(logger, "后台初始化完成");
    }
    catch (Exception ex)
    {
        var logger = app.Services.GetRequiredService<ILogger<Program>>();
        Microsoft.Extensions.Logging.LoggerExtensions.LogError(logger, ex, "后台初始化失败");
    }
});

Console.WriteLine("[STARTUP] 开始启动应用程序...");
Console.WriteLine($"[STARTUP] 应用程序将监听: {string.Join(", ", app.Urls)}");
app.Run();
Console.WriteLine("[STARTUP] 应用程序已停止");

/// <summary>
/// 配置NATS日志发布
/// </summary>
static void ConfigureNatsLogging(IServiceProvider serviceProvider)
{
    try
    {
        // 创建新的Logger配置，添加NATS Sink
        var loggerConfig = new LoggerConfiguration()
            .ReadFrom.Configuration(serviceProvider.GetRequiredService<IConfiguration>())
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.AspNetCore", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("System.Net.Http.HttpClient", Serilog.Events.LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .Enrich.WithProperty("Application", "FlowCustomV1")
            .Enrich.WithProperty("MachineName", Environment.MachineName);

        // 添加现有的输出配置
        var environment = serviceProvider.GetRequiredService<IWebHostEnvironment>();
        if (environment.IsDevelopment())
        {
            loggerConfig.WriteTo.Console(
                outputTemplate: "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}",
                restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Debug);
        }
        else
        {
            // 在生产环境中也输出到控制台以便调试
            loggerConfig.WriteTo.Console(
                outputTemplate: "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}",
                restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Information);

            loggerConfig.WriteTo.File(
                path: "logs/flowcustom-.log",
                rollingInterval: Serilog.RollingInterval.Day,
                retainedFileCountLimit: 30,
                restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Information,
                outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}");
        }

        // 添加NATS Sink
        loggerConfig.WriteTo.Nats(serviceProvider, Serilog.Events.LogEventLevel.Warning);

        // 更新全局Logger
        Log.Logger = loggerConfig.CreateLogger();
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"配置NATS日志失败: {ex.Message}");
    }
}


