using Serilog;
using Serilog.Configuration;
using Serilog.Events;
using FlowCustomV1.Api.Logging;

namespace FlowCustomV1.Api.Logging
{
    /// <summary>
    /// Serilog NATS扩展方法
    /// </summary>
    public static class SerilogNatsExtensions
    {
        /// <summary>
        /// 添加NATS Sink到Serilog配置
        /// </summary>
        public static LoggerConfiguration Nats(
            this LoggerSinkConfiguration sinkConfiguration,
            IServiceProvider serviceProvider,
            LogEventLevel restrictedToMinimumLevel = LogEventLevel.Warning)
        {
            if (sinkConfiguration == null)
                throw new ArgumentNullException(nameof(sinkConfiguration));

            if (serviceProvider == null)
                throw new ArgumentNullException(nameof(serviceProvider));

            return sinkConfiguration.Sink(
                new NatsSink(serviceProvider),
                restrictedToMinimumLevel);
        }
    }
}
