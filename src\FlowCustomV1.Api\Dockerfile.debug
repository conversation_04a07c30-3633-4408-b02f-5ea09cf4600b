# 调试版本的Dockerfile - 简化版本
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

# 安装调试工具
RUN apt-get update && apt-get install -y \
    curl \
    procps \
    htop \
    && rm -rf /var/lib/apt/lists/*

# 设置UTF-8环境
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 构建阶段
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制项目文件
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]
COPY ["src/FlowCustomV1.Data/FlowCustomV1.Data.csproj", "src/FlowCustomV1.Data/"]
COPY ["src/FlowCustomV1.Engine/FlowCustomV1.Engine.csproj", "src/FlowCustomV1.Engine/"]
COPY ["src/FlowCustomV1.PluginHost/FlowCustomV1.PluginHost.csproj", "src/FlowCustomV1.PluginHost/"]
COPY ["src/FlowCustomV1.SDK/FlowCustomV1.SDK.csproj", "src/FlowCustomV1.SDK/"]

# 还原依赖
RUN dotnet restore "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj"

# 复制源代码
COPY . .
WORKDIR "/src/src/FlowCustomV1.Api"

# 构建应用
RUN dotnet build "FlowCustomV1.Api.csproj" -c Release -o /app/build

# 发布阶段
FROM build AS publish
RUN dotnet publish "FlowCustomV1.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# 最终镜像
FROM base AS final
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/data /app/plugins /app/logs

# 复制发布的应用
COPY --from=publish /app/publish .

# 插件目录已在上面创建，跳过复制（避免构建错误）

# 设置环境变量
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# 添加启动脚本
RUN echo '#!/bin/bash\n\
echo "[DOCKER] 容器启动开始..."\n\
echo "[DOCKER] 当前时间: $(date)"\n\
echo "[DOCKER] 工作目录: $(pwd)"\n\
echo "[DOCKER] 文件列表:"\n\
ls -la\n\
echo "[DOCKER] 环境变量:"\n\
env | grep -E "(ASPNETCORE|DOTNET|LANG|LC_)"\n\
echo "[DOCKER] 开始启动应用程序..."\n\
exec dotnet FlowCustomV1.Api.dll\n\
' > /app/start.sh && chmod +x /app/start.sh

ENTRYPOINT ["/app/start.sh"]
